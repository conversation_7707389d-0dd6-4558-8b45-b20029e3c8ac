stages:
  - check
  - build

default:
  image: *************/vpm/vpm-base:jdk8-mvn

before_script:
  - >-
    echo '<settings><mirrors><mirror><id>nexus-private</id><url>http://*************:8081/repository/maven-public/</url><mirrorOf>*</mirrorOf></mirror></mirrors></settings>' > $MAVEN_HOME/conf/settings.xml

variables:
  MAVEN_OPTS: >-
    -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository

check-vpm:
  stage: check
  rules:
    - exists:
        - $CI_COMMIT_TAG
      when: never
    - when: always
  script:
    - mvn -DskipTests -Drevision=0.0.1 -f $CI_PROJECT_DIR/pom.xml compile

build-vpm:
  stage: build
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - export CI_COMMIT_TAG_MODIFIED=$(echo $CI_COMMIT_TAG | cut -c 2-)
    - mvn -DskipTests -Drevision=$CI_COMMIT_TAG_MODIFIED -f $CI_PROJECT_DIR/pom.xml package
  artifacts:
    name: CaesarVPM-${CI_COMMIT_TAG}
    expire_in: 1 day
    paths:
      - mc-caesar-vpm/target/CaesarVPM-${CI_COMMIT_TAG}.exe
      - mc-caesar-vpm/target/proguard_map.txt
