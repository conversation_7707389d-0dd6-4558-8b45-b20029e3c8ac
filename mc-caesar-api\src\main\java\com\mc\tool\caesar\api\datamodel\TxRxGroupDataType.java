package com.mc.tool.caesar.api.datamodel;

import lombok.Getter;

/**
 * 分组数据类型.
 */
public enum TxRxGroupDataType {
  TX(0), // TX普通分组，兼容旧版本TX分组
  RX(16), // RX普通分组
  RX_AUDIO_GROUP(32); // RX音频镜像分组

  @Getter
  private final int value;

  TxRxGroupDataType(int value) {
    this.value = value;
  }

  /**
   * 根据value获取TxRxGroupDataType.
   */
  public static TxRxGroupDataType valueOf(int value) {
    for (TxRxGroupDataType type : TxRxGroupDataType.values()) {
      if (type.getValue() == value) {
        return type;
      }
    }
    return TX;
  }
}
