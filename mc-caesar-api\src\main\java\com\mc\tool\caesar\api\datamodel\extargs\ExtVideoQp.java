package com.mc.tool.caesar.api.datamodel.extargs;

import com.mc.tool.caesar.api.Bundle;
import lombok.Getter;

/**
 * .
 */
@Getter
public enum ExtVideoQp implements ExtArgObject {
  QP_1(1, "QP_1"), QP_2(2, "QP_2"), QP_3(3, "QP_3"), QP_4(4, "QP_4"),
  QP_5(5, "QP_5"), QP_6(6, "QP_6"), QP_7(7, "QP_7"), QP_8(8, Bundle.videoQp8());

  private final int value;
  private final String name;

  ExtVideoQp(int value, String name) {
    this.value = value;
    this.name = name;
  }

  @Override
  public byte[] toBytes() {
    return new byte[]{(byte) getValue()};
  }

  @Override
  public String toString() {
    return name;
  }

}
