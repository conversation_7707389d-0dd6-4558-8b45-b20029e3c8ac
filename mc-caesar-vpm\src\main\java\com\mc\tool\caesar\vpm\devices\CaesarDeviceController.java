package com.mc.tool.caesar.vpm.devices;

import com.google.common.eventbus.Subscribe;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.User;
import com.mc.tool.caesar.api.CaesarControllerConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpType;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.caesar.vpm.device.broadcast.BroadcastServerManager;
import com.mc.tool.caesar.vpm.device.broadcast.GridBroadcastServer;
import com.mc.tool.caesar.vpm.event.GridEvent;
import com.mc.tool.caesar.vpm.kaito.ApiClient;
import com.mc.tool.caesar.vpm.kaito.api.KaitoApi;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.usb.UsbBindingConfigBean;
import com.mc.tool.caesar.vpm.userright.CaesarAdminUserRight;
import com.mc.tool.caesar.vpm.userright.CaesarCommonUserRight;
import com.mc.tool.caesar.vpm.userright.CaesarConfigFileUserRightDecorator;
import com.mc.tool.caesar.vpm.userright.CaesarSubMatrixUserRightDecorator;
import com.mc.tool.caesar.vpm.util.VpmVersionUtility;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.UserRightGetter;
import com.mc.tool.framework.utility.EventBusProvider;
import java.beans.PropertyChangeListener;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarDeviceController implements DeviceControllable {
  public static final int MAX_TASK_COUNT = 5;
  @Getter protected CaesarSwitchDataModel dataModel;
  @Getter protected final String loginUser;
  @Getter protected final String loginPwd;
  @Getter protected final String defaultIp;

  @Getter
  protected ObjectProperty<CaesarDeviceConnections> connections = new SimpleObjectProperty<>();

  @Getter protected BooleanProperty isClose = new SimpleBooleanProperty(false);

  protected ScheduledThreadPoolExecutor executor =
      new ScheduledThreadPoolExecutor(
          1,
          new ThreadFactoryBuilder()
              .setDaemon(true)
              .setNameFormat(CaesarConstants.DATA_THREAD_PREFIX + "%d")
              .setUncaughtExceptionHandler((thread, ex) -> {
                log.error("Error in thread {}", thread, ex);
              })
              .build(),
          new CallerRunsPolicy());

  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected AtomicInteger updateCount = new AtomicInteger(0);

  // 三期新增:双主控运行状态
  @Getter private final BooleanProperty hasMatrixInfo = new SimpleBooleanProperty(false);
  @Getter private final BooleanProperty active = new SimpleBooleanProperty(); // 指示当前主控板处于激活状态
  @Getter private final BooleanProperty backup = new SimpleBooleanProperty(); // 指示当前主控板是备份主控板

  private KaitoApi kaitoApi;

  /**
   * Constructor.
   *
   * @param dataModel data model
   */
  public CaesarDeviceController(
      CaesarSwitchDataModel dataModel, String user, String pwd, String defaultIp) {
    this.dataModel = dataModel;
    this.loginUser = user;
    this.loginPwd = pwd;
    this.defaultIp = defaultIp;
    updateConnections();
    dataModel.addPropertyChangeListener(
        new String[] {
          CpuData.PROPERTY_CONSOLE,
          CpuData.PROPERTY_STATUS,
          ConsoleData.PROPERTY_CPU,
          ConsoleData.PROPERTY_RDCPU,
          ConsoleData.PROPERTY_STATUS,
          ExtenderData.PROPERTY_STATUS,
          ExtenderData.PROPERTY_PORT,
          ExtenderData.PROPERTY_RDPORT,
          PortData.PROPERTY_Status,
          PortData.PROPERTY_OUTPUT
        },
        weakAdapter.wrap(
            (PropertyChangeListener)
                evt -> {
                  if (dataModel.isUpdating()) {
                    return;
                  }
                  updateConnections();
                }));
    dataModel.addPropertyChangeListener(
        new String[] {CaesarSwitchDataModel.PROPERTY_RECERTIFICATION},
        weakAdapter.wrap(
            (PropertyChangeListener)
                evt -> {
                  try {
                    dataModel.login(
                        loginUser.getBytes(StandardCharsets.UTF_8),
                        loginPwd.getBytes(StandardCharsets.UTF_8), VpmVersionUtility.makeVpmVersion());
                  } catch (ConfigException | BusyException ex) {
                    log.error(loginUser + ", authentication failure", ex);
                  }
                }));
  }

  public void init() {
    EventBusProvider.getEventBus().register(this);
    BroadcastServerManager.createServer(GridBroadcastServer.getInstance());
  }

  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  public void endUpdate() {
    updateCount.decrementAndGet();
  }

  public boolean isUpdating() {
    return updateCount.get() != 0;
  }

  @Subscribe
  protected void onReceivedGridInfo(GridEvent gridEvent) {
    if (isDemo()) {
      return;
    }
    if (gridEvent.getType()
        == CaesarControllerConstants.BroadcastRequest.SET_GRIDMASTER.getByteValue()) {
      // 更新主用矩阵索引
      boolean gridEnable =
          getDataModel().getConfigData().getSystemConfigData().isMatrixGridEnabled();
      String systemname =
          getDataModel().getConfigData().getSystemConfigData().getSystemData().getName();
      if (gridEnable && systemname.equals(gridEvent.getGridData().getGridname())) {
        getDataModel().getConfigData().setGridIndex(gridEvent.getGridData().getIndex());
      }
    } else if (gridEvent.getType()
        == CaesarControllerConstants.BroadcastRequest.GET_MATRIX_ACTIVE.getByteValue()) {
      // 更新双主控状态
      hasMatrixInfo.set(false);
      if (defaultIp.equals(gridEvent.getInetAddress().getHostAddress())
          && gridEvent.getGridData().isHasMatrixInfo()) {
        hasMatrixInfo.set(true);
        active.set(gridEvent.getGridData().isActive());
        backup.set(gridEvent.getGridData().isBackup());

      }
    }
  }

  /** 检查是否有太多任务在运行. */
  public boolean checkCanExecute() {
    return executor.getQueue().size() + executor.getActiveCount() < MAX_TASK_COUNT;
  }

  @Override
  public boolean isDemo() {
    return false;
  }

  public void execute(Runnable runnable) {
    executor.execute(runnable);
  }

  public ScheduledFuture<?> scheduleExecute(
      Runnable runnable, long initialDelay, long delay, TimeUnit unit) {
    return executor.scheduleWithFixedDelay(runnable, initialDelay, delay, unit);
  }

  public Future<?> submit(Runnable runnable) {
    return executor.submit(runnable);
  }

  public <T> Future<T> submit(Callable<T> callable) {
    return executor.submit(callable);
  }

  public CompletableFuture<?> submitAsync(Runnable runnable) {
    return CompletableFuture.runAsync(runnable, executor);
  }

  public <T> CompletableFuture<T> submitAsync(Supplier<T> supplier) {
    return CompletableFuture.supplyAsync(supplier, executor);
  }

  /** 更新设备连接的关系. */
  public synchronized void updateConnections() {
    if (dataModel == null) {
      return;
    }
    CaesarDeviceConnections newConnections = new CaesarDeviceConnections();
    updateGeneralConnections(newConnections);
    updateMultiviewConnections(newConnections);
    this.connections.set(newConnections);
  }

  private void updateGeneralConnections(CaesarDeviceConnections newConnections) {
    for (CpuData cpuData : dataModel.getConfigDataManager().getActiveCpus()) {
      if (!cpuData.isOnline()) {
        continue;
      }
      ExtenderData cpuExtenderData = cpuData.getExtenderData(0);
      if (cpuExtenderData == null) {
        continue;
      }
      ConsoleData connectedConsole = cpuData.getConsoleData();
      for (ConsoleData consoleData : dataModel.getConfigDataManager().getActiveConsoles()) {
        if (!consoleData.isOnline()) {
          continue;
        }
        ExtenderData consoleExtenderData = consoleData.getExtenderData(0);
        if (consoleExtenderData == null) {
          continue;
        }
        Pair<Integer, Integer> ids = null;
        SwitchType type = null;
        if (connectedConsole == consoleData && !consoleData.isMultiview()) {
          if (consoleData.getCpuData() != cpuData) {
            log.warn("{} connects {}, but {} does not connect {}!", cpuData.getName(),
                consoleData.getName(), consoleData.getName(), cpuData.getName());
          } else {
            if (cpuData.isStatusPrivate()) {
              ids = new Pair<>(cpuExtenderData.getId(), consoleExtenderData.getId());
              type = SwitchType.PRIVATE;
            } else {
              ids = new Pair<>(cpuExtenderData.getId(), consoleExtenderData.getId());
              type = SwitchType.FULL;
            }
          }
        } else if (consoleData.getCpuData() == cpuData) {
          ids = new Pair<>(cpuExtenderData.getId(), consoleExtenderData.getId());
          type = SwitchType.VIDEO;
        }
        if (ids == null) {
          continue;
        }
        PortData conNormalOutputPort = null;
        PortData conNormalPort = consoleExtenderData.getPortData();
        if (conNormalPort != null) {
          conNormalOutputPort = consoleExtenderData.getPortData().getOutputPortData();
        }
        PortData conRdOutputPort = null;
        PortData conRdPort = consoleExtenderData.getRdPortData();
        if (conRdPort != null) {
          conRdOutputPort = consoleExtenderData.getRdPortData().getOutputPortData();
        }

        PortData cpuNormalPort = cpuExtenderData.getPortData();
        PortData cpuRdPort = cpuExtenderData.getRdPortData();

        CaesarDeviceConnections conns = new CaesarDeviceConnections();
        if (conNormalPort != null
            && cpuNormalPort != null
            && conNormalOutputPort == cpuNormalPort) {
          conns.put(createConnection(ids, false, false), type);
        }
        if (conNormalPort != null && cpuRdPort != null && conNormalOutputPort == cpuRdPort) {
          conns.put(createConnection(ids, true, false), type);
        }
        if (conRdPort != null && cpuNormalPort != null && conRdOutputPort == cpuNormalPort) {
          conns.put(createConnection(ids, false, true), type);
        }
        if (conRdPort != null && cpuRdPort != null && conRdOutputPort == cpuRdPort) {
          conns.put(createConnection(ids, true, true), type);
        }
        if (conns.isEmpty()) {
          conns.put(createConnection(ids, false, false), type);
        }
        newConnections.putAll(conns);
      } // end for
    }
  }

  private void updateMultiviewConnections(CaesarDeviceConnections newConnections) {
    for (ConsoleData conData : dataModel.getConfigDataManager().getActiveConsoles()) {
      if (!conData.isOnline()) {
        continue;
      }
      ExtenderData conExtData = conData.getExtenderData(0);
      if (conExtData == null || !conData.isMultiview()) {
        continue;
      }
      MultiviewData multiviewData = conData.getMultiviewData();
      List<SwitchType> types = ExtendedSwitchUtility.getAllSwitchType(conData, dataModel);
      if (multiviewData != null && types.size() == multiviewData.getSourceCount()) {
        for (int ch = 0; ch < multiviewData.getSourceCount(); ch++) {
          if (types.get(ch) == SwitchType.NOT_SWITCHED) {
            continue;
          }
          MultiviewSource source = multiviewData.getSource(ch);
          if (source == null) {
            log.error("Multiview source is null at channel {}", ch);
            continue;
          }
          CpuData cpuData = source.getCpuData();
          if (cpuData != null && cpuData.isOnline()) {
            ExtenderData cpuExtData = cpuData.getExtenderData(0);
            if (cpuExtData == null) {
              continue;
            }
            PortData conNormalOutputPort = null;
            PortData conNormalPort = conExtData.getPortData();
            if (conNormalPort != null) {
              conNormalOutputPort = conExtData.getPortData().getOutputPortData();
            }
            PortData conRdOutputPort = null;
            PortData conRdPort = conExtData.getRdPortData();
            if (conRdPort != null) {
              conRdOutputPort = conExtData.getRdPortData().getOutputPortData();
            }
            PortData cpuNormalPort = cpuExtData.getPortData();
            PortData cpuRdPort = cpuExtData.getRdPortData();
            CaesarDeviceConnections conns = new CaesarDeviceConnections();
            SwitchType type = types.get(ch);
            if (conNormalPort != null
                && cpuNormalPort != null
                && conNormalOutputPort == cpuNormalPort) {
              conns.put(createConnection(cpuExtData, conExtData, false, false, ch), type);
            }
            if (conNormalPort != null && cpuRdPort != null && conNormalOutputPort == cpuRdPort) {
              conns.put(createConnection(cpuExtData, conExtData, true, false, ch), type);
            }
            if (conRdPort != null && cpuNormalPort != null && conRdOutputPort == cpuNormalPort) {
              conns.put(createConnection(cpuExtData, conExtData, false, true, ch), type);
            }
            if (conRdPort != null && cpuRdPort != null && conRdOutputPort == cpuRdPort) {
              conns.put(createConnection(cpuExtData, conExtData, true, true, ch), type);
            }
            if (conns.isEmpty()) {
              conns.put(createConnection(cpuExtData, conExtData, false, false, ch), type);
            }
            newConnections.putAll(conns);
          }
        }
      }
    }
  }

  private CaesarDeviceConnection createConnection(
      Pair<Integer, Integer> cpuConIdPair, boolean isCpuRd, boolean isConRd) {
    CaesarDeviceConnection connection = new CaesarDeviceConnection();
    connection.setCpuExtenderId(cpuConIdPair.getKey());
    connection.setConExtenderId(cpuConIdPair.getValue());
    connection.setCpuRedundant(isCpuRd);
    connection.setConRedundant(isConRd);
    return connection;
  }

  private CaesarDeviceConnection createConnection(ExtenderData cpuExtenderData,
                                                  ExtenderData conExtenderData, boolean isCpuRd,
                                                  boolean isConRd, int conChannel) {
    return CaesarDeviceConnection.builder()
        .cpuExtenderId(cpuExtenderData.getId())
        .conExtenderId(conExtenderData.getId())
        .isCpuRedundant(isCpuRd)
        .isConRedundant(isConRd)
        .conChannel(conChannel).build();
  }

  /**
   * 添加跨屏数据.
   *
   * @param data 跨屏数据.
   */
  public void addMultiScreenData(MultiScreenData data) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) add multi screen data", loginUser);
    List<MultiScreenData> multiScreenChangeList = new ArrayList<>();
    List<ConsoleData> consoleChangeList = new ArrayList<>();
    for (int i = 0; i < CaesarConstants.MultiScreen.MAX_CON; i++) {
      ConsoleData console =
          dataModel.getConfigDataManager().getConsoleData4Id(data.getConInfo(i).getId());
      if (console != null) {
        consoleChangeList.add(console);
      }
    }

    data.setStatusNew(false);
    multiScreenChangeList.add(data);

    for (ConsoleData consoleData : consoleChangeList) {
      if (consoleData != null) {
        consoleData.setMultiScreenIndex(data.getOid() + 1);
      }
    }
    try {
      dataModel.sendMultiscreenData(multiScreenChangeList);
      dataModel.sendConsoleData(consoleChangeList);
    } catch (DeviceConnectionException | BusyException exception) {
      log.warn("Fail to add multi screen data!", exception);
    }
  }

  /**
   * 删除设备的视频墙数据.
   *
   * @param index 视频墙索引
   */
  public void deleteVideoWall(int index) {
    log.info("User({}) delete video wall[{}] ", loginUser, index);
    if (index < 0 || index >= VideoWallGroupData.GROUP_COUNT) {
      return;
    }

    execute(
        () -> {
          try {
            getDataModel().beginUpdateVideoWall();
          } catch (Exception exception) {
            log.warn("Fail to begin update video wall!", exception);
            return;
          }
          try {
            getDataModel().getVpDataModel().sendVideoWallData(index, new VideoWallData());
            for (int i = index * VideoWallGroupData.VIDEOWALL_SCENARIO_COUNT;
                i < (index + 1) * VideoWallGroupData.VIDEOWALL_SCENARIO_COUNT;
                i++) {
              getDataModel().getVpDataModel().removeScenarioData(i);
            }
          } catch (Exception exception) {
            log.warn("Fail to send video wall data!", exception);
          } finally {
            try {
              getDataModel().endUpdateVideoWall();
            } catch (Exception exception2) {
              log.warn("Fail to end update video wall!", exception2);
            }
          }
        });
  }

  /** 添加TXRX分组数据. */
  public void addTxRxGroupData(TxRxGroupData data) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) add {} tx groups", loginUser, data.getName());
    switch (data.getType()) {
      case TX:
        List<CpuData> cpuChangeList = new ArrayList<>();
        for (CpuData cpu : data.getConfigDataManager().getCpus()) {
          if (cpu.getTxGroupIndex() == data.getOid() + 1) {
            cpuChangeList.add(cpu);
          }
        }
        data.setStatusActive(true);

        try {
          dataModel.sendTxRxGroupData(Collections.singletonList(data));
          if (!cpuChangeList.isEmpty()) {
            dataModel.sendCpuData(cpuChangeList);
          }
        } catch (DeviceConnectionException | BusyException exception) {
          log.warn("Fail to add tx group data!", exception);
        }
        break;
      case RX_AUDIO_GROUP:
        List<ConsoleData> consoleChangeList = new ArrayList<>();
        for (ConsoleData console : data.getConfigDataManager().getAllConsoles()) {
          if (console.getGroupIndex() == data.getOid() + 1) {
            consoleChangeList.add(console);
          }
        }
        data.setStatusActive(true);
        try {
          dataModel.sendTxRxGroupData(Collections.singletonList(data));
          if (!consoleChangeList.isEmpty()) {
            dataModel.sendConsoleData(consoleChangeList);
          }
        } catch (DeviceConnectionException | BusyException exception) {
          log.warn("Fail to add audio group data!", exception);
        }
        break;
      default:
        break;
    }
  }

  /** 删除设备的TXRX分组数据. */
  public void deleteTxRxGroup(TxRxGroupData data) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) delete {} tx groups", loginUser, data.getName());
    switch (data.getType()) {
      case TX:
        List<CpuData> cpuChangeList = new ArrayList<>();
        for (CpuData cpu : data.getConfigDataManager().getCpus()) {
          if (cpu.getTxGroupIndex() == data.getOid() + 1) {
            cpu.setTxGroupIndex(0);
            cpuChangeList.add(cpu);
          }
        }
        data.delete();
        try {
          dataModel.sendTxRxGroupData(Collections.singletonList(data));
          if (!cpuChangeList.isEmpty()) {
            dataModel.sendCpuData(cpuChangeList);
          }
        } catch (DeviceConnectionException | BusyException exception) {
          // 回滚
          data.rollback();
          cpuChangeList.forEach(item -> item.rollbackProperty(CpuData.PROPERTY_GROUP_INDEX));
          log.warn("Fail to delete tx group data!", exception);
        }
        break;
      case RX_AUDIO_GROUP:
        List<ConsoleData> consoleChangeList = new ArrayList<>();
        for (ConsoleData console : data.getConfigDataManager().getAllConsoles()) {
          if (console.getGroupIndex() == data.getOid() + 1) {
            console.setGroupIndex(0);
            consoleChangeList.add(console);
          }
        }
        // 删除数据
        data.delete();
        try {
          dataModel.sendTxRxGroupData(Collections.singletonList(data));
          if (!consoleChangeList.isEmpty()) {
            dataModel.sendConsoleData(consoleChangeList);
          }
        } catch (DeviceConnectionException | BusyException exception) {
          // 回滚
          data.rollback();
          consoleChangeList.forEach(
              item -> item.rollbackProperty(ConsoleData.PROPERTY_GROUP_INDEX));
          log.warn("Fail to delete audio group data!", exception);
        }
        break;
      default:
        break;
    }
  }

  /** 直接删除TXRX分组. */
  public void deleteTxRxGroupsDirectly(List<TxRxGroupData> txRxGroups) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    txRxGroups.forEach(item -> {
      log.info("User({}) delete {} txrx groups", loginUser, item.getName());
      item.delete();
    });
    try {
      dataModel.sendTxRxGroupData(txRxGroups);
    } catch (DeviceConnectionException | BusyException exception) {
      txRxGroups.forEach(TxRxGroupData::rollback);
      log.warn("Fail to delete tx group data!", exception);
    }
  }

  /** 电源pdu开启. */
  public void switchPduPowerOn(CpuData cpuData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) power on {} pdu", loginUser, new String(cpuData.getPduIp(), StandardCharsets.UTF_8));
    try {
      dataModel.switchPowerControl(cpuData, 1);
    } catch (DeviceConnectionException | BusyException exception) {
      log.warn("Fail to switch Power On!", exception);
    }
  }

  /** 电源pdu关闭. */
  public void switchPduPowerOff(CpuData cpuData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) power off {} pdu", loginUser, new String(cpuData.getPduIp(), StandardCharsets.UTF_8));
    try {
      dataModel.switchPowerControl(cpuData, 2);
    } catch (DeviceConnectionException | BusyException exception) {
      log.warn("Fail to switch Pdu Power Off!", exception);
    }
  }

  /** 电源pdu重启. */
  public void switchPduReboot(CpuData cpuData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) reboot {} pdu ", loginUser, new String(cpuData.getPduIp(), StandardCharsets.UTF_8));
    try {
      dataModel.switchPowerControl(cpuData, 3);
    } catch (DeviceConnectionException | BusyException exception) {
      log.warn("Fail to switch Pdu Reboot!", exception);
    }
  }

  /**
   * 断开vpcon的所有连接.
   *
   * @param vpConsoleDatas vpcon
   */
  public void resetVpCons(Collection<VpConsoleData> vpConsoleDatas) {
    log.info("User({}) reset vp con", loginUser);
    if (vpConsoleDatas == null || vpConsoleDatas.size() == 0) {
      return;
    }
    Map<ConsoleData, CpuData> connections = new HashMap<>();
    for (VpConsoleData vpConsoleData : vpConsoleDatas) {
      if (vpConsoleData == null) {
        continue;
      }
      for (ConsoleData consoleData : vpConsoleData.getInPortList()) {
        if (consoleData == null) {
          continue;
        }
        connections.put(consoleData, null);
      }
    }
    execute(() -> {
      try {
        getDataModel().getController().setCpuConsoleConnectionBlock(connections);
        for (VpConsoleData vpConsoleData : vpConsoleDatas) {
          if (vpConsoleData.getType() == VpType.VP6) {
            vpConsoleData.setConfigData(new Vp6ConfigData());
          } else if (vpConsoleData.getType() == VpType.VP7) {
            vpConsoleData.setConfigData(new Vp7ConfigData());
          }
          getDataModel().sendVpconConfigData(vpConsoleData);
        }
      } catch (DeviceConnectionException | ConfigException | BusyException exception) {
        log.warn("Fail to disconnect vpcons!", exception);
      }
    });
  }

  /**
   * 删除跨屏.
   *
   * @param data 跨屏数据.
   */
  public void deleteMultiScreenData(MultiScreenData data) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) delete multi screen data", loginUser);
    List<MultiScreenData> multiScreenDataChangeList = new ArrayList<>();
    List<ConsoleData> consoleDataChangeList = new ArrayList<>();

    for (int i = 0; i < CaesarConstants.MultiScreen.MAX_CON; i++) {
      ConsoleData console =
          dataModel.getConfigDataManager().getConsoleData4Id(data.getConInfo(i).getId());
      consoleDataChangeList.add(console);
    }
    data.delete();
    multiScreenDataChangeList.add(data);

    if (!consoleDataChangeList.isEmpty()) {
      for (ConsoleData consoleData : consoleDataChangeList) {
        if (consoleData != null) {
          consoleData.setMultiScreenIndex(0);
        }
      }
    }
    try {
      dataModel.sendMultiscreenData(multiScreenDataChangeList);
      dataModel.sendConsoleData(consoleDataChangeList);
    } catch (DeviceConnectionException | BusyException exception) {
      log.warn("Fail to delete multi screen data!", exception);
    }
  }

  /**
   * 添加USB端口绑定.
   *
   * @param model datamodel
   * @param config 端口绑定配置
   * @param extenderData 要绑定的外设
   */
  public void addUsbBinding(
      CaesarSwitchDataModel model, UsbBindingConfigBean config, ExtenderData extenderData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({})  add usb binding", loginUser);
    addUsbBindingImpl(model, config, extenderData);
  }

  protected void addUsbBindingImpl(
      CaesarSwitchDataModel model, UsbBindingConfigBean config, ExtenderData extenderData) {
    extenderData.setId(model.getConfigDataManager().getAutoIdExtender());
    extenderData.setName(
        String.format(CaesarConstants.Extender.NAME_FORMAT_STRING, extenderData.getId()));
    if (config.type.get() == UsbBindingConfigBean.UsbType.USB_TX) {
      extenderData.setType(CaesarConstants.Extender.Type.USB_CPU);
      CpuData cpuData = (CpuData) config.binding.get();
      if (cpuData != null) {
        // 删除旧的绑定
        ExtenderData oldExt = cpuData.getExtenderData(1);
        if (oldExt != null) {
          deleteUsbExtenderImpl(oldExt);
        }
        if (!cpuData.isExtenderFull()) {
          cpuData.addExtender(extenderData);
          extenderData.setCpuData(cpuData);
        }
      } else {
        log.error(
            String.format(
                "Fail to add usb(%s) binding cause by cpuData is null", extenderData.getName()));
        return;
      }
      extenderData.setStatusActive(true);
      extenderData.setPort(config.getPort());

      PortData portData = extenderData.getPortData();
      if (portData != null) {
        portData.setExtenderData(extenderData);
        portData.setType(extenderData.getType());
        portData.setStatusActive(true);
        portData.setStatusAvailable(true);
      } else {
        log.error(
            String.format(
                "Fail to add usb(%s) binding cause by portData is null", extenderData.getName()));
        return;
      }
      try {
        getDataModel().setUsbBinding(true, portData, extenderData, cpuData);
      } catch (ConfigException exception) {
        log.error("Fail to add usb binding!", exception);
      }
    } else if (config.type.get() == UsbBindingConfigBean.UsbType.USB_RX) {
      extenderData.setType(CaesarConstants.Extender.Type.USB_CON);
      ConsoleData consoleData = (ConsoleData) config.binding.get();
      if (consoleData != null) {
        // 删除旧的绑定
        ExtenderData oldExt = consoleData.getExtenderData(1);
        if (oldExt != null) {
          deleteUsbExtenderImpl(oldExt);
        }
        if (!consoleData.isExtenderFull()) {
          consoleData.addExtender(extenderData);
          extenderData.setConsoleData(consoleData);
        }
      } else {
        log.error(
            String.format(
                "Fail to add usb(%s) binding cause by ConsoleData is null",
                extenderData.getName()));
        return;
      }
      extenderData.setStatusActive(true);
      extenderData.setPort(config.getPort());

      PortData portData = extenderData.getPortData();
      if (portData != null) {
        portData.setExtenderData(extenderData);
        portData.setType(extenderData.getType());
        portData.setStatusActive(true);
        portData.setStatusAvailable(true);
      } else {
        log.error(
            String.format(
                "Fail to add usb(%s) binding cause by portData is null", extenderData.getName()));
        return;
      }
      try {
        getDataModel().setUsbBinding(true, portData, extenderData, consoleData);
      } catch (ConfigException exception) {
        log.error("Fail to add usb binding!", exception);
      }
    }
  }

  /**
   * 删除USB外设.
   *
   * @param extenderData USB外设数据
   */
  public void deleteUsbExtender(ExtenderData extenderData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) delete usb extender", loginUser);
    if (extenderData == null) {
      return;
    }
    deleteUsbExtenderImpl(extenderData);
  }

  protected void deleteUsbExtenderImpl(ExtenderData extenderData) {
    if (extenderData == null) {
      return;
    }
    PortData portData = extenderData.getPortData();
    if (portData != null) {
      portData.setStatus(0);
      portData.setExtenderData(null);
      portData.setType(0);
    } else {
      return;
    }

    extenderData.setStatusActive(false);
    extenderData.setPort(0);
    extenderData.setRdPort(0);

    if (extenderData.getCpuData() != null) {
      CpuData cpuData = extenderData.getCpuData();
      cpuData.removeExtender(extenderData);
      extenderData.setCpuCon(0);
      try {
        getDataModel().setUsbBinding(false, portData, extenderData, cpuData);
      } catch (ConfigException exception) {
        log.error("Fail to remove usb extender!", exception);
      }
    }
    if (extenderData.getConsoleData() != null) {
      ConsoleData consoleData = extenderData.getConsoleData();
      consoleData.removeExtender(extenderData);
      extenderData.setCpuCon(0);
      try {
        getDataModel().setUsbBinding(false, portData, extenderData, consoleData);
      } catch (ConfigException exception) {
        log.error("Fail to remove usb extender!", exception);
      }
    }
  }

  /**
   * 重新绑定扩展器.
   *
   * @param usbExtender usb extender
   * @param newBind 新绑定的扩展器
   */
  public void rebindUsbExtender(ExtenderData usbExtender, DataObject newBind) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) rebind usb extender", loginUser);
    if (usbExtender == null) {
      return;
    }

    if (usbExtender.getCpuData() != null) {
      CpuData cpuData = usbExtender.getCpuData();
      cpuData.removeExtender(usbExtender);
      try {
        getDataModel().setUsbBinding(false, usbExtender.getPortData(), usbExtender, cpuData);
      } catch (ConfigException exception) {
        log.error("Fail to remove usb extender!", exception);
      }
    }
    if (usbExtender.getConsoleData() != null) {
      ConsoleData consoleData = usbExtender.getConsoleData();
      consoleData.removeExtender(usbExtender);
      try {
        getDataModel().setUsbBinding(false, usbExtender.getPortData(), usbExtender, consoleData);
      } catch (ConfigException exception) {
        log.error("Fail to remove usb extender!", exception);
      }
    }

    if (newBind instanceof CpuData) {
      CpuData cpuData = (CpuData) newBind;
      cpuData.addExtender(usbExtender);
      usbExtender.setCpuData(cpuData);
      try {
        getDataModel().setUsbBinding(true, usbExtender.getPortData(), usbExtender, cpuData);
      } catch (ConfigException exception) {
        log.error("Fail to add usb binding!", exception);
      }
    }
    if (newBind instanceof ConsoleData) {
      ConsoleData consoleData = (ConsoleData) newBind;
      consoleData.addExtender(usbExtender);
      usbExtender.setConsoleData(consoleData);
      try {
        getDataModel().setUsbBinding(true, usbExtender.getPortData(), usbExtender, consoleData);
      } catch (ConfigException exception) {
        log.error("Fail to add usb binding!", exception);
      }
    }
  }

  /** 双主控对端重启. */
  public void dualMatrixReboot() {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) reboot dual matrix", loginUser);
    try {
      dataModel.dualMatrixReboot();
    } catch (BusyException | ConfigException exception) {
      log.warn("Fail to reboot opposite matrix!", exception);
    }
  }

  /**
   * .
   *
   * @brief 向设备发送cpu数据
   */
  public void sendCpuData(Iterable<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    log.info("User({}) send cpu data", loginUser);
    try {
      dataModel.sendCpuData(cpuDatas);
    } catch (BusyException | DeviceConnectionException exception) {
      log.warn("Fail to reboot opposite matrix!", exception);
    }
  }

  @Override
  public boolean isOnline() {
    return dataModel.connectedProperty().get();
  }

  @Override
  public BooleanProperty onlineProperty() {
    return dataModel.connectedProperty();
  }

  @Override
  public void close() {
    if (executor != null) {
      executor.shutdown();
      ExecutorService es = executor;
      CaesarDeviceController controller = this;
      Thread thread =
          new Thread(
              () -> {
                try {
                  while (!es.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("Waiting for device controller termination!");
                  }
                } catch (InterruptedException exception) {
                  log.info("Await termination is interrupted!", exception);
                }
                log.info("Device controller has terminated!");
                controller.dataModel = null;
              });
      thread.setDaemon(true);
      thread.start();
      executor = null;
    }
    EventBusProvider.getEventBus().unregister(this);
  }

  @Override
  public UserRightGetter getUserRight() {
    return getCaesarUserRight();
  }

  /**
   * 获取权限.
   *
   * @return 权限信息.
   */
  public CaesarUserRightGetter getCaesarUserRight() {
    if (dataModel.isOnlyConfig()) {
      return new CaesarConfigFileUserRightDecorator(getCaesarUserRightImpl());
    } else if (isSubMatrix()) {
      return new CaesarSubMatrixUserRightDecorator(getCaesarUserRightImpl());
    } else {
      return getCaesarUserRightImpl();
    }
  }

  private CaesarUserRightGetter getCaesarUserRightImpl() {
    UserData loginUserData = null;
    for (UserData userData : dataModel.getConfigDataManager().getActiveUsers()) {
      if (userData.getName().equals(loginUser)) {
        loginUserData = userData;
        break;
      }
    }
    if (loginUserData == null || loginUserData.hasRightAdmin()) {
      return new CaesarAdminUserRight();
    } else if (loginUserData.hasRightAdministrator()) {
      return new CaesarAdminUserRight();
    } else {
      return new CaesarCommonUserRight(loginUserData.getVideoWallRights());
    }
  }

  /**
   * 当前登录用户是否为管理员.
   */
  public boolean isLoginUserAdmin() {
    return isAdminUser(loginUser);
  }

  /**
   * 是否为子主机.
   *
   * @return 如果为子主机，返回true
   */
  public boolean isSubMatrix() {
    if (dataModel.getConfigData().getSystemConfigData().isMatrixGridEnabled()) {
      String ip =
          IpUtil.getAddressString(
              dataModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataCurrent1()
                  .getAddress());
      int index = dataModel.getConfigData().getGridIndex() - 1;
      if (index >= 0 && index < dataModel.getConfigMetaData().getMatrixCount()) {
        MatrixData matrixData = dataModel.getConfigData().getMatrixData(index);
        return !matrixData.getHostAddressString().equals(ip);
      }
    }
    return false;
  }

  /**
   * isAdminUser.
   */
  public boolean isAdminUser(String userName) {
    if (User.ADMINISTRATOR_NAME.equals(userName)) {
      return true;
    }
    boolean isAdmin = false;
    for (UserData userData : dataModel.getConfigDataManager().getActiveUsers()) {
      if (userName.equals(userData.getName())) {
        isAdmin = userData.hasRightAdmin();
        break;
      }
    }
    return isAdmin;
  }

  /** 检查用户名是否符合规范. */
  public boolean checkUsernameValidity(String userName) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    try {
      // Skip checking Admin
      if (CaesarConstants.User.ADMIN_NAME.equals(userName)) {
        return true;
      }
      return dataModel.checkUsernameValidity(userName);
    } catch (BusyException | DeviceConnectionException | ConfigException ex) {
      log.error("check Username Validity failed", ex);
      return false;
    }
  }

  /**
   * 获取kaitoApi.
   */
  public KaitoApi getKaitoApi() {
    if (kaitoApi == null) {
      if ("demo.status".equals(defaultIp)) {
        // 使用apifox的mock数据，打开apifox即可
        kaitoApi = new ApiClient().setBasePath("http://127.0.0.1:4523/m1/1699539-0-default/").buildClient(KaitoApi.class);
      } else {
        kaitoApi = new ApiClient().setBasePath("http://" + defaultIp + ":8080").buildClient(KaitoApi.class);
      }
    }
    return kaitoApi;
  }
}
