package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.mc.common.util.PlatformUtility;
import com.mc.graph.McGraph;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.GraphObject;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderInfo.ImportResult;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CaesarHotkeyConfigurationPage;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarData2VisualDataModel;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.graph.CaesarSystemEditGraph;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuAudioGroup4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuCrossScreen4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuDelete4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuDisconnect;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuGroup4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuGroupingRx4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuGroupingTx4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuIrregularCrossScreen4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuMultiScreen4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuQuickCreateCrossScreen;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuQuickCreateMultiScreen;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuQuickCreateVideoWall;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuUsb;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuUsbRebind;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuVideoWall4Caesar;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.MenuVideoWall4CaesarReuse;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarConPropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarCpuPropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarProperty;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.data.GridMatrixItem;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.graph.TopologicalGraph;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.graph.TopologicalGraphWrapper;
import com.mc.tool.caesar.vpm.pages.systemedit.view.BranchSettingView;
import com.mc.tool.caesar.vpm.pages.systemedit.view.ConnectDialog;
import com.mc.tool.caesar.vpm.pages.systemedit.view.OfflineManagerView;
import com.mc.tool.caesar.vpm.pages.systemedit.view.RemoteSwitchingView;
import com.mc.tool.caesar.vpm.pages.systemedit.view.TerminalListView;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.caesar.vpm.util.ReconnectDevicesUtility;
import com.mc.tool.caesar.vpm.util.ReloadUtility;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.controller.GraphWrapper;
import com.mc.tool.framework.systemedit.controller.NodeSearcher;
import com.mc.tool.framework.systemedit.controller.SystemEditHighLightHelper;
import com.mc.tool.framework.systemedit.controller.SystemEditMatrixWrapper;
import com.mc.tool.framework.systemedit.controller.SystemEditPageController;
import com.mc.tool.framework.systemedit.datamodel.AbstractVisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.MultiScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.NodeUtility;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.graph.SystemEditGraph;
import com.mc.tool.framework.systemedit.menu.MenuDeviceType;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.utility.dialogues.FileDialogueManager;
import java.io.File;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.application.Platform;
import javafx.beans.binding.ListBinding;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ContextMenu;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.PropertySheet.Item;

/**
 * .
 */
@Slf4j
public class CaesarSystemEditController extends SystemEditPageController {

  private static final String[] CSV_EXTENSION = {"CSV", "*.csv"};
  private static final String[] EXCEL_EXTENSION = {"Excel", "*.xlsx", "*.xls"};

  @FXML
  private HBox connectionFilterBox;
  @FXML
  private VBox listView;
  @FXML
  private VBox graphView;
  @FXML
  private VBox remoteSwitchingContainer;
  @FXML
  private TerminalListView terminalListView;
  @FXML
  private ComboBox<String> pageSelector;
  @FXML
  private RemoteSwitchingView remoteSwitchingView;

  @FXML
  private VBox branchSettingContainer;
  @FXML
  private BranchSettingView branchSettingView;

  @FXML
  private VBox offlineContainer;
  @FXML
  private OfflineManagerView offlineManagerView;

  private WeakReference<CaesarDeviceController> deviceController = null;
  private Entity entity = null;

  private static final Color FULL_ACCESS_COLOR = Color.rgb(0, 128, 0).brighter();
  private static final Color PRIVATE_ACCESS_COLOR = FULL_ACCESS_COLOR;
  private static final Color VIDEO_ACCESS_COLOR = Color.rgb(255, 170, 0);
  private DefaultConnectionFilter connectionFilter;

  private CaesarData2VisualDataModel dataConverter = new CaesarData2VisualDataModel();
  private CaesarConnectionHelper connectionHelper = new CaesarConnectionHelper();

  private ScheduledFuture<?> reloadConnectionScheduledFuture;

  @Override
  public void initModel(VisualEditModel model) {
    super.initModel(model);
    connectionHelper.setModel(model);
    dataConverter.setModel(model);
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    initConnectionFilter();
    initImpl();
    systemEditModel.getConnections()
        .addListener(weakAdapter.wrap(connectionHelper.createConnectionChangeListener()));

    masterDetailPane.managedProperty().bind(masterDetailPane.visibleProperty());
    listView.managedProperty().bind(listView.visibleProperty());
    graphView.managedProperty().bind(graphView.visibleProperty());
    remoteSwitchingContainer.managedProperty().bind(remoteSwitchingContainer.visibleProperty());
    branchSettingContainer.managedProperty().bind(branchSettingContainer.visibleProperty());
    offlineContainer.managedProperty().bind(offlineContainer.visibleProperty());
    pageSelector.getSelectionModel().selectedIndexProperty()
        .addListener((observable, oldValue, newValue) -> {
          switch (newValue.intValue()) {
            case 0:
              listView.setVisible(false);
              remoteSwitchingContainer.setVisible(false);
              branchSettingContainer.setVisible(false);
              offlineContainer.setVisible(false);
              masterDetailPane.setVisible(true);
              graphView.setVisible(true);
              connectionFilter.getRoot().setVisible(true);
              VisualEditTerminal selectedItem =
                  terminalListView.getSelectionModel().getSelectedItem();
              if (selectedItem != null) {
                CellSkin graphSkin =
                    getGraph().getSkinManager().getCellSkin(selectedItem.getCellObject());
                if (graphSkin != null
                    && (getGraph().getSelectionModel().getSelectedItems().size() != 1
                    || !getGraph()
                    .getSelectionModel()
                    .getSelectedItems()
                    .contains(graphSkin))) {
                  getGraph().getSelectionModel().deselectAll();
                  getGraph().getSelectionModel().select(graphSkin, true);
                }
              }
              break;
            case 1:
              connectionFilter.getRoot().setVisible(false);
              graphView.setVisible(false);
              remoteSwitchingContainer.setVisible(false);
              branchSettingContainer.setVisible(false);
              offlineContainer.setVisible(false);
              masterDetailPane.setVisible(true);
              listView.setVisible(true);
              // 让listview选择graph中选中的内容
              VisualEditTerminal graphSelectedItem = null;
              for (CellSkin skin : getGraph().getSelectionModel().getSelectedCellSkin()) {
                if (skin.getCell() != null
                    && skin.getCell().getBindedObject() != null
                    && skin.getCell().getBindedObject() instanceof CaesarTerminalBase) {
                  graphSelectedItem = (CaesarTerminalBase) skin.getCell().getBindedObject();
                  break;
                }
              }
              terminalListView.getSelectionModel().select(graphSelectedItem);
              break;
            case 2:
              masterDetailPane.setVisible(false);
              connectionFilter.getRoot().setVisible(false);
              branchSettingContainer.setVisible(false);
              offlineContainer.setVisible(false);
              remoteSwitchingContainer.setVisible(true);
              break;
            case 3:
              masterDetailPane.setVisible(false);
              connectionFilter.getRoot().setVisible(false);
              remoteSwitchingContainer.setVisible(false);
              branchSettingContainer.setVisible(true);
              offlineContainer.setVisible(false);
              branchSettingView.refreshData();
              break;
            case 4:
              masterDetailPane.setVisible(false);
              connectionFilter.getRoot().setVisible(false);
              remoteSwitchingContainer.setVisible(false);
              branchSettingContainer.setVisible(false);
              offlineContainer.setVisible(true);
              offlineManagerView.refreshData();
              break;
            default:
              break;
          }
        });
    updatePageSelector();
    Consumer<VisualEditTerminal> nameHandler = terminal -> {
      Predicate<String> predicate = new CaesarNamePredicate();
      Optional<String> result =
          ViewUtility.getNameFromDialog(
              graphContainer.getScene().getWindow(), terminal.getName(), null, predicate);
      if (result.isPresent()) {
        if (terminal instanceof CaesarConTerminal) {
          CaesarConTerminal conTerminal = (CaesarConTerminal) terminal;
          CaesarConPropertySender<String> sender =
              new CaesarConPropertySender<>(
                  getDeviceController(),
                  conTerminal.getConsoleData(),
                  ConsoleData.PROPERTY_NAME);
          sender.sendValue(result.get());
        } else if (terminal instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) terminal;
          CaesarCpuPropertySender<String> sender =
              new CaesarCpuPropertySender<>(
                  getDeviceController(), cpuTerminal.getCpuData(), CpuData.PROPERTY_NAME);
          sender.sendValue(result.get());
        } else {
          terminal.setName(result.get());
        }
      }
    };

    Consumer<VisualEditTerminal> idHandler = terminal -> {
      if (CaesarPropertyCreater.changeIdWarning()) {
        if (terminal instanceof CaesarConTerminal) {
          CaesarConTerminal conTerminal = (CaesarConTerminal) terminal;
          CaesarConPropertySender<Number> idPropertySender =
              new CaesarConPropertySender<>(
                  getDeviceController(), conTerminal.getConsoleData(), ConsoleData.PROPERTY_ID);
          CaesarProperty<Number> idProperty =
              new CaesarProperty<>(
                  conTerminal.getConsoleData().getIdProperty(), idPropertySender);
          CaesarPropertyCreater.configConId(
              getDeviceController(), conTerminal.getConsoleData().getId() + "", idProperty);
          idProperty.addListener(
              (observable, oldVal, newVal) -> {
                if (newVal.intValue() != 0
                    && oldVal.intValue() != 0
                    && !newVal.equals(oldVal)) {
                  refresh();
                }
              });
        } else if (terminal instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) terminal;
          CaesarCpuPropertySender<Number> idPropertySender =
              new CaesarCpuPropertySender<>(
                  getDeviceController(), cpuTerminal.getCpuData(), CpuData.PROPERTY_ID);
          CaesarProperty<Number> idProperty =
              new CaesarProperty<>(cpuTerminal.getCpuData().getIdProperty(), idPropertySender);
          idProperty.addListener((observable, oldVal, newVal) -> {
            if (newVal.intValue() != 0
                && oldVal.intValue() != 0
                && !newVal.equals(oldVal)) {
              refresh();
            }
          });
          CaesarPropertyCreater.configCpuId(
              getDeviceController(), cpuTerminal.getCpuData().getId() + "", idProperty);
        }
      }
    };

    terminalListView.init(systemEditModel, idHandler, nameHandler);
    remoteSwitchingView.init(systemEditModel);

    // 当listview选择项时graph选择对应的项
    terminalListView.getSelectionModel().selectedItemProperty()
        .addListener((observable, oldValue, newValue) -> {
          if (newValue == null) {
            return;
          }
          if (!listView.isVisible()) {
            // 当terminal list view显示时才修改graph的选择，避免操作graph时出现逻辑问题.
            return;
          }
          CellSkin skin = getGraph().getSkinManager().getCellSkin(newValue.getCellObject());
          if (skin != null
              && (getGraph().getSelectionModel().getSelectedItems().size() != 1
              || !getGraph().getSelectionModel().getSelectedItems().contains(skin))) {
            getGraph().getSelectionModel().deselectAll();
            getGraph().getSelectionModel().select(skin, true);
          }
        });
  }

  @Override
  public NodeSearcher createNodeSearcher() {
    return new CaesarNodeSearcher(systemEditModel.getRoots());
  }

  @Override
  protected void attachGraph(GraphWrapper graphWrapper) {
    if (graphWrapper.isAttached()) {
      return;
    }
    super.attachGraph(graphWrapper);
    EventHandler<MouseEvent> eventFilter = event -> {
      if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 2) {
        // 如果没有权限，不允许修改名称.
        if (!getDeviceController().getUserRight().isSystemEditRenamable()) {
          event.consume();
          return;
        }
        // 修改cpu或con的名称
        Collection<VisualEditNode> nodes = getSelectedNodes();
        if (nodes.size() == 1) {
          VisualEditNode node = nodes.iterator().next();
          // 矩阵不能修改名称
          if (node instanceof VisualEditMatrix
              || node instanceof CaesarUsbRxTerminal
              || node instanceof CaesarUsbTxTerminal) {
            event.consume();
            return;
          }
          if (node instanceof AbstractVisualEditNode) {
            List<String> existingNames = null;
            if (node instanceof CaesarCpuTerminal) {
              existingNames =
                  getDeviceController().getDataModel().getConfigDataManager().getCpus().stream()
                      .map(CpuData::getName)
                      .collect(Collectors.toList());
              existingNames.remove(node.getName());
            } else if (node instanceof CaesarConTerminal) {
              existingNames =
                  getDeviceController()
                      .getDataModel()
                      .getConfigDataManager()
                      .getActiveConsoles()
                      .stream()
                      .map(ConsoleData::getName)
                      .collect(Collectors.toList());
              existingNames.remove(node.getName());
            } else if (node instanceof CpuGroup || node instanceof AudioGroup) {
              existingNames =
                  getDeviceController()
                      .getDataModel()
                      .getConfigDataManager()
                      .getActiveTxRxGroups()
                      .stream()
                      .map(TxRxGroupData::getName)
                      .collect(Collectors.toList());
              existingNames.remove(node.getName());
            } else if (node instanceof CaesarVideoWallFunc) {
              existingNames = new ArrayList<>();
              for (int videoWallIndex = 0; videoWallIndex < VideoWallGroupData.GROUP_COUNT;
                   videoWallIndex++) {
                VideoWallGroupData.VideoWallData videoWallData =
                    getDeviceController()
                        .getDataModel()
                        .getVpDataModel()
                        .getVideoWallData(videoWallIndex);
                if (!videoWallData.getName().equals(node.getName())
                    && !videoWallData.getName().isEmpty()) {
                  existingNames.add(videoWallData.getName());
                }
              }
            }
            Predicate<String> predicate = new CaesarNamePredicate(existingNames);
            Optional<String> result =
                ViewUtility.getNameFromDialog(
                    graphContainer.getScene().getWindow(), node.getName(), null, predicate);
            if (result.isPresent() && !node.getName().equals(result.get())) {
              node.setName(result.get());
              if (node instanceof CaesarCpuTerminal) {
                // 发送cpu数据
                CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) node;
                if (cpuTerminal.getCpuData() != null) {
                  getDeviceController()
                      .execute(
                          () -> {
                            cpuTerminal.getCpuData().setName(result.get());
                            CaesarDeviceController deviceController = getDeviceController();
                            if (deviceController == null) {
                              return;
                            }
                            try {
                              deviceController
                                  .getDataModel()
                                  .sendCpuData(
                                      Collections.singletonList(cpuTerminal.getCpuData()));
                            } catch (DeviceConnectionException | BusyException exception) {
                              log.warn("Fail to send cpu data!", exception);
                            }
                          });
                }
              } else if (node instanceof CaesarConTerminal) {
                // 发送con数据
                CaesarConTerminal conTerminal = (CaesarConTerminal) node;
                if (conTerminal.getConsoleData() != null) {
                  getDeviceController().execute(() -> {
                    conTerminal.getConsoleData().setName(result.get());
                    CaesarDeviceController deviceController = getDeviceController();
                    if (deviceController == null) {
                      return;
                    }
                    try {
                      deviceController.getDataModel().sendConsoleData(
                          Collections.singletonList(conTerminal.getConsoleData()));
                    } catch (DeviceConnectionException | BusyException exception) {
                      log.warn("Fail to send console data!", exception);
                    }
                  });
                }
              } else if (node instanceof CpuGroup) {
                // 发送Tx分组数据
                CpuGroup cpuGroup = (CpuGroup) node;
                if (cpuGroup.getTxRxGroupData() != null) {
                  getDeviceController().execute(() -> {
                    try {
                      cpuGroup.getTxRxGroupData().setName(result.get());
                      getDeviceController().getDataModel()
                          .sendTxRxGroupData(Collections.singletonList(cpuGroup.getTxRxGroupData()));
                    } catch (DeviceConnectionException | BusyException exception) {
                      log.warn("Fail to send cpu group data!", exception);
                    }
                  });
                }
              } else if (node instanceof AudioGroup) {
                // 发送音频镜像分组数据
                AudioGroup audioGroup = (AudioGroup) node;
                if (audioGroup.getTxRxGroupData() != null) {
                  getDeviceController().execute(() -> {
                    try {
                      audioGroup.getTxRxGroupData().setName(result.get());
                      getDeviceController().getDataModel()
                         .sendTxRxGroupData(Collections.singletonList(audioGroup.getTxRxGroupData()));
                    } catch (DeviceConnectionException | BusyException exception) {
                      log.warn("Fail to send audio group data!", exception);
                    }
                  });
                }
              }
            }
            event.consume();
          }
        }
      }
    };
    graphWrapper.attachClickEventFilter(eventFilter);
  }

  @Override
  protected void updateProperties(McGraph graph) {
    if (graph.getSelectionModel().getSelectedItems().isEmpty()) {
      aggregatedProperties.clearList();
      aggregatedNodeActions.clearList();
    } else {
      SelectableNode node = graph.getSelectionModel().getSelectedItems().get(0);
      CellBindedObject cellBindedObject = NodeUtility.selectedNode2CellBindedObject(node);
      aggregatedProperties.clearList();
      aggregatedNodeActions.clearList();
      if (cellBindedObject instanceof VisualEditNode) {
        VisualEditNode visualEditNode = (VisualEditNode) cellBindedObject;
        aggregatedProperties.appendList(createNewProperties(visualEditNode));
        aggregatedNodeActions.appendList(createNodeActions(visualEditNode));
        if (visualEditNode instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) visualEditNode;
          cpuTerminal.cpuDataProperty().addListener((observable, oldVal, newVal) -> {
            if (Objects.requireNonNull(oldVal.get()).getId()
                != Objects.requireNonNull(newVal.get()).getId()) {
              aggregatedProperties.clearList();
              aggregatedNodeActions.clearList();
              aggregatedProperties.appendList(createNewProperties(visualEditNode));
              aggregatedNodeActions.appendList(createNodeActions(visualEditNode));
            }
          });
        } else if (visualEditNode instanceof CaesarConTerminal) {
          CaesarConTerminal conTerminal = (CaesarConTerminal) visualEditNode;
          conTerminal.conDataProperty().addListener((observable, oldVal, newVal) -> {
            if (Objects.requireNonNull(oldVal.get()).getId()
                != Objects.requireNonNull(newVal.get()).getId()) {
              aggregatedProperties.clearList();
              aggregatedNodeActions.clearList();
              aggregatedProperties.appendList(createNewProperties(visualEditNode));
              aggregatedNodeActions.appendList(createNodeActions(visualEditNode));
            }
          });
        }
      } else if (cellBindedObject instanceof GridMatrixItem) {
        GridMatrixItem gridMatrixItem = (GridMatrixItem) cellBindedObject;
        aggregatedProperties.appendList(
            FXCollections.observableArrayList(
                CaesarPropertyCreater.createGridMatrixItemProperties(gridMatrixItem)));
        aggregatedNodeActions.appendList(
            FXCollections.observableArrayList(
                new TypeWrapper(
                    CaesarI18nCommonResource.getString("systemedit.switch"),
                    SystemEditDefinition.ACTION_SWITCH_MATRIX,
                    "",
                    gridMatrixItem.getName())));
      }
    }
  }

  @Override
  protected ObservableList<Item> createNewProperties(VisualEditNode node) {
    if (node instanceof CaesarTerminalBase || node instanceof CaesarGridLineTerminal) {
      ListBinding<Item> binding =
          new ListBinding<Item>() {
            {
              bind(node.getProperties(), systemEditModel.getConnections());
            }

            @Override
            protected ObservableList<Item> computeValue() {
              Version version =
                  getDeviceController().getDataModel().getConfigMetaData().getUtilVersion();
              ObservableList<Item> result = FXCollections.observableArrayList();
              result.addAll(node.getProperties());
              CaesarMatrix matrix = CaesarDataUtility.findCaesarMatrix(node);
              CaesarPropertyCreater propertyCreater =
                  new CaesarPropertyCreater(getDeviceController(), entity);
              result.addAll(propertyCreater.createExtraProperties(matrix, node, version));
              return result;
            }
          };
      binding.get();
      return binding;
    } else if (node instanceof CaesarMatrix) {
      ListBinding<Item> binding =
          new ListBinding<Item>() {
            {
              bind(node.getProperties());
            }

            @Override
            protected ObservableList<Item> computeValue() {
              Version version =
                  getDeviceController().getDataModel().getConfigMetaData().getUtilVersion();
              ObservableList<Item> result = FXCollections.observableArrayList();
              result.addAll(node.getProperties());
              CaesarMatrix matrix = CaesarDataUtility.findCaesarMatrix(node);
              CaesarPropertyCreater propertyCreater =
                  new CaesarPropertyCreater(getDeviceController(), entity);
              result.addAll(propertyCreater.createExtraProperties(matrix, node, version));
              return result;
            }
          };
      binding.get();
      return binding;
    } else if (node instanceof VisualEditGroup) {
      ListBinding<Item> binding =
          new ListBinding<Item>() {
            {
              bind(systemEditModel.getConnections());
            }

            @Override
            protected ObservableList<Item> computeValue() {
              ObservableList<Item> result = FXCollections.observableArrayList();
              CaesarMatrix matrix = CaesarDataUtility.findCaesarMatrix(node);
              if (node instanceof VideoWallFunc
                  || node instanceof CrossScreenFunc
                  || node instanceof VpGroup
                  || node instanceof CpuGroup
                  || node instanceof AudioGroup) {
                Version version =
                    getDeviceController().getDataModel().getConfigMetaData().getUtilVersion();
                CaesarPropertyCreater propertyCreater =
                    new CaesarPropertyCreater(getDeviceController(), entity);
                result.addAll(propertyCreater.createExtraProperties(matrix, node, version));
              } else {
                result.addAll(node.getProperties());
              }
              return result;
            }
          };
      binding.get();
      return binding;
    } else {
      return super.createNewProperties(node);
    }
  }

  @Override
  protected ObservableList<TypeWrapper> createNodeActions(VisualEditNode node) {
    ObservableList<TypeWrapper> nodeActions = FXCollections.observableArrayList();
    if (node instanceof CaesarMatrix) {
      nodeActions.add(new TypeWrapper(
          NbBundle.getMessage("ActionType.reboot"), SystemEditDefinition.ACTION_REBOOT, ""));
      nodeActions.add(new TypeWrapper(
          NbBundle.getMessage("ActionType.reset"), SystemEditDefinition.ACTION_RESET, ""));
    } else if (node instanceof CaesarConTerminal && !((CaesarConTerminal) node).isVp()) {
      CaesarUserRightGetter userRightGetter = getDeviceController().getCaesarUserRight();
      if (userRightGetter.isPageVisible(CaesarPermissionConfigurationPage.NAME)) {
        nodeActions.add(new TypeWrapper(
            NbBundle.getMessage("Goto_btn.scan"), SystemEditDefinition.ACTION_SCAN, ""));
        nodeActions.add(new TypeWrapper(
            NbBundle.getMessage("Goto_btn.right"), SystemEditDefinition.ACTION_RIGHT, ""));
      }
      if (userRightGetter.isPageVisible(CaesarHotkeyConfigurationPage.NAME)) {
        nodeActions.add(new TypeWrapper(
            NbBundle.getMessage("Goto_btn.macro"), SystemEditDefinition.ACTION_MACRO, ""));
      }
    }
    return nodeActions;
  }

  @Override
  protected void onNodeAction(VisualEditNode node, TypeWrapper wrapper) {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
    if (graphContainer != null && graphContainer.getScene() != null) {
      alert.initOwner(graphContainer.getScene().getWindow());
    }
    alert.setHeaderText(null);
    alert.getButtonTypes().add(ButtonType.CANCEL);
    switch (wrapper.getType()) {
      case SystemEditDefinition.ACTION_REBOOT:
        alert.setTitle(NbBundle.getMessage("alert.reboot.title"));
        alert.setContentText(NbBundle.getMessage("alert.reboot.text"));
        alert.showAndWait().ifPresent(type -> {
          if (type.equals(ButtonType.OK)) {
            log.info("reboot matrix.");
            getDeviceController().execute(() -> {
              CaesarDeviceController deviceController = getDeviceController();
              if (deviceController == null) {
                return;
              }
              deviceController.getDataModel().checkConnectivity();
              if (deviceController.getDataModel().isConnected()) {
                try {
                  deviceController.getDataModel().restartAll(0);
                } catch (ConfigException | BusyException ex) {
                  log.error("Failed to reboot!");
                }
              }
              // 重启所有子矩阵
              if (!deviceController.isSubMatrix()) {
                for (MatrixDefinitionData data :
                    Utilities.getActiveMatrices(deviceController.getDataModel())) {
                  try {
                    CaesarSwitchDataModel model =
                        Utilities.getExternalModel(
                            deviceController.getDataModel(), data.getAddress());
                    if (model == deviceController.getDataModel()) {
                      continue;
                    }
                    model.restartAll(0);
                  } catch (BusyException | ConfigException exception) {
                    log.warn(
                        "Fail to get external model for {}!", data.getAddress());
                  }
                }
              }
            });
            ReconnectDevicesUtility.waitingForReconnection(app, entity, getDeviceController(),
                false);
          }
        });
        break;
      case SystemEditDefinition.ACTION_RESET:
        alert.setTitle(NbBundle.getMessage("alert.reset.title"));
        alert.setContentText(NbBundle.getMessage("alert.reset.text"));
        alert.showAndWait().ifPresent(type -> {
          if (type.equals(ButtonType.OK)) {
            log.info("reset matrix.");
            getDeviceController().execute(() -> {
              CaesarDeviceController deviceController = getDeviceController();
              if (deviceController == null) {
                return;
              }
              int p1 =
                  deviceController
                      .getDataModel()
                      .getConfigMetaData()
                      .getUtilVersion()
                      .getDeviceFactoryResetSlot();
              try {
                deviceController.getDataModel().factoryReset(p1);
              } catch (ConfigException | BusyException ex) {
                log.error("Failed to reset!");
              }
            });
            ReconnectDevicesUtility.waitingForReconnection(
                app, entity, getDeviceController(), true);
          }
        });
        break;
      case SystemEditDefinition.ACTION_SCAN:
        app.getViewManager().switchToPage(this.getEntity(), CaesarHotkeyConfigurationPage.NAME,
            new Pair<>(CaesarHotkeyConfigurationPage.CON_HOTKET_TAB,
                ((CaesarConTerminal) node).getConsoleData()));
        break;
      case SystemEditDefinition.ACTION_RIGHT:
        app.getViewManager().switchToPage(this.getEntity(), CaesarPermissionConfigurationPage.NAME,
            new Pair<>(CaesarPermissionConfigurationPage.CON_RIGHT_TAB,
                ((CaesarConTerminal) node).getConsoleData()));
        break;
      case SystemEditDefinition.ACTION_MACRO:
        app.getViewManager().switchToPage(this.getEntity(), CaesarHotkeyConfigurationPage.NAME,
            new Pair<>(CaesarHotkeyConfigurationPage.CON_MACRO_TAB,
                ((CaesarConTerminal) node).getConsoleData()));
        break;
      case SystemEditDefinition.ACTION_SWITCH_MATRIX:
        if (wrapper.getParams().length > 0) {
          String name = wrapper.getParams()[0];
          for (GraphWrapper graphWrapper : graphWrappers) {
            if (graphWrapper.getName().equals(name)
                && graphWrapper instanceof SystemEditMatrixWrapper) {
              currentGraph.set(graphWrapper);
              break;
            }
          }
        }
        break;
      default:
        break;
    }
  }

  @Override
  public void selectAndCenterNode(VisualEditNode node) {
    super.selectAndCenterNode(node);
    if (node instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) node;
      terminalListView.scrollTo(terminal);
      terminalListView.getSelectionModel().select(terminal);
    } else {
      terminalListView.getSelectionModel().select(null);
    }
  }

  private void initConnectionFilter() {
    connectionFilter = new DefaultConnectionFilter();
    connectionFilterBox.getChildren().add(connectionFilter.getRoot());
    HBox.setHgrow(connectionFilter.getRoot(), Priority.ALWAYS);
  }

  @Override
  public void setEntity(Entity entity) {
    this.entity = entity;
  }

  @Override
  public Entity getEntity() {
    return entity;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (!(deviceController instanceof CaesarDeviceController)) {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }

    this.deviceController = new WeakReference<>((CaesarDeviceController) deviceController);
    this.dataConverter.setDeviceController(getDeviceController());
    this.connectionHelper.setDeviceController(getDeviceController());
    this.remoteSwitchingView.setDeviceController(getDeviceController());
    this.branchSettingView.setDeviceController(getDeviceController());
    this.offlineManagerView.setDataConverter(dataConverter);

    getDeviceController().beginUpdate();
    try {
      loadData(false);
    } finally {
      getDeviceController().endUpdate();
    }

    connectionHelper.updateConnections(this.getDeviceController().getConnections().get());

    this.getDeviceController().getConnections().addListener(weakAdapter.wrap(
        (obs, oldVal, newVal) -> Platform.runLater(() -> connectionHelper.updateConnections(
            this.getDeviceController().getConnections().get()))));

    reloadConnectionScheduledFuture = this.getDeviceController().scheduleExecute(
        () -> {
          CaesarDeviceController controller = getDeviceController();
          if (controller != null
              && !controller.getIsClose().get()
              && controller.isOnline()) {
            reloadConnections();
            PlatformUtility.runInFxThread(this::updateGridLineStatus);
          }
        },
        3,
        3, TimeUnit.SECONDS);

    this.getDeviceController().getDataModel()
        .addPropertyChangeListener(ConfigData.PROPERTY_GRID_INDEX,
            evt -> PlatformUtility.runInFxThread(this::updateMatrixName));
    // 避免错过消息
    PlatformUtility.runInFxThread(this::updateMatrixName);
    //
    this.getDeviceController()
        .getDataModel()
        .addPropertyChangeListener(
            ModuleData.PROPERTY_TYPE,
            evt -> PlatformUtility.runInFxThread(this::updatePageSelector));
    //
    PlatformUtility.runInFxThread(this::updatePageSelector);
  }

  private void updateMatrixName() {
    for (MatrixDefinitionData data :
        Utilities.getActiveMatrices(getDeviceController().getDataModel())) {
      CaesarMatrix matrix = findCaesarMatrix(data);
      matrix.setName(createMatrixName(data));
    }
  }

  private void updatePageSelector() {
    ObservableList<String> options =
        FXCollections.observableArrayList(
            CaesarI18nCommonResource.getString("systemedit.page.selector.graph"),
            CaesarI18nCommonResource.getString("systemedit.page.selector.list"),
            CaesarI18nCommonResource.getString("systemedit.page.selector.switch"));
    ModuleData module;
    CaesarConstants.Module.Type type;
    if (getDeviceController() != null && getDeviceController().getDataModel() != null
        && (module = getDeviceController().getDataModel().getSwitchModuleData().getModuleData(0))
        != null
        && (type = CaesarConstants.Module.Type.valueOf(module.getType())) != null
        && getDeviceController().getDataModel().getConfigMetaData().getUtilVersion().hasBranchData()
        && (type != CaesarConstants.Module.Type.SINGLE_CPU || module.getPorts() == 36)) {
      options.add(CaesarI18nCommonResource.getString("systemedit.page.selector.speed"));
    }
    options.add(CaesarI18nCommonResource.getString("systemedit.page.selector.offline"));

    pageSelector.setItems(options);
    if (pageSelector.getSelectionModel().getSelectedItem() == null) {
      pageSelector.getSelectionModel().selectFirst();
    }
  }

  @Override
  public CaesarDeviceController getDeviceController() {
    if (deviceController == null) {
      return null;
    }
    return deviceController.get();
  }

  private void reloadConnections() {
    try {
      CaesarSwitchDataModel model = getDeviceController().getDataModel();
      model.getSwitchModuleData().requestPorts();
      model.reloadCpuConsoleMatrix();
      getDeviceController().updateConnections();
    } catch (ConfigException | BusyException exception) {
      log.warn("Fail to reload connections!", exception);
    }
  }

  /**
   * load data.
   */
  public void loadData(boolean onlyConnection) {
    if (this.getDeviceController() != null) {
      final long tic = System.currentTimeMillis();
      clearAllGraph();
      List<GraphWrapper> retainWrappers = new ArrayList<>();
      List<CaesarMatrix> retainCaesarMatrices = new ArrayList<>();
      Collection<MatrixDefinitionData> datas =
          Utilities.getActiveMatrices(getDeviceController().getDataModel());
      dataConverter.checkErrors();
      final long tic1 = System.currentTimeMillis();
      for (MatrixDefinitionData data : datas) {
        CaesarMatrix matrix = findCaesarMatrix(data);
        retainWrappers.add(findMatrixWrapper(matrix));
        retainCaesarMatrices.add(matrix);
        dataConverter.loadData(matrix, onlyConnection);
      }
      final long toc1 = System.currentTimeMillis();
      log.info("convert data time {}ms", toc1 - tic1);
      // 必须放在加载matrix之后
      if (datas.size() > 1) {
        TopologicalGraphWrapper wrapper =
            new TopologicalGraphWrapper(
                new TopologicalGraph(), dataConverter.loadMatrixTopological(retainCaesarMatrices));
        retainWrappers.add(wrapper);
        graphWrappers.add(0, wrapper);
      }
      connectionHelper.loadConnections();
      graphWrappers.retainAll(retainWrappers);
      systemEditModel.getRoots().retainAll(retainCaesarMatrices);
      updateModelToAllGraph();
      final long toc = System.currentTimeMillis();
      log.info("load data time {}ms", toc - tic);
    }
  }

  protected SystemEditMatrixWrapper findMatrixWrapper(VisualEditMatrix matrix) {
    // 先查找相关的matrix
    for (GraphWrapper wrapper : graphWrappers) {
      if (wrapper instanceof SystemEditMatrixWrapper
          && ((SystemEditMatrixWrapper) wrapper).getMatrix() == matrix) {
        return (SystemEditMatrixWrapper) wrapper;
      }
    }
    // 如果wrapper的matrix为空，就用空的
    for (GraphWrapper wrapper : graphWrappers) {
      if (wrapper instanceof SystemEditMatrixWrapper
          && ((SystemEditMatrixWrapper) wrapper).getMatrix() == null) {
        SystemEditMatrixWrapper matrixWrapper = (SystemEditMatrixWrapper) wrapper;
        matrixWrapper.setMatrix(matrix);
        return matrixWrapper;
      }
    }
    // 都找不到，就创建一个
    SystemEditMatrixWrapper matrixWrapper =
        new SystemEditMatrixWrapper(systemEditModel, createSystemEditGraph(), onlineEdit);
    matrixWrapper.setMatrix(matrix);
    graphWrappers.add(matrixWrapper);
    return matrixWrapper;
  }

  @Override
  public Collection<TypeWrapper> getConnectableType(
      VisualEditTerminal rx, Object rxConnector, VisualEditTerminal tx, Object txConnector) {
    return TerminalUtility.getConnectableType(getDeviceController(), rx, tx);
  }

  /**
   * 获取Multiview通道列表.
   */
  @Override
  public Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx, VisualEditTerminal tx) {
    return TerminalUtility.getMultiviewChannel(rx);
  }

  @Override
  public void connect(
      VisualEditTerminal tx,
      ConnectorIdentifier txConnector,
      VisualEditTerminal rx,
      ConnectorIdentifier rxConnector,
      String mode, int rxChannel) {
    if (rx instanceof CaesarGridLineTerminal && tx instanceof CaesarCpuTerminal) {
      CaesarGridLineTerminal gridLineTerminal = (CaesarGridLineTerminal) rx;
      PortData portData = gridLineTerminal.getPortData();
      if (portData == null) {
        log.error("{}'s port data is null!", gridLineTerminal.getName());
        return;
      }
      // 查找gridline所连接的matrix
      MatrixDefinitionData matrixDefinitionData = null;
      for (MatrixDefinitionData data :
          Utilities.getActiveMatrices(getDeviceController().getDataModel())) {
        if (data.getFirstPort() <= portData.getType() && portData.getType() <= data.getLastPort()) {
          matrixDefinitionData = data;
          break;
        }
      }
      if (matrixDefinitionData == null) {
        return;
      }
      CaesarMatrix caesarMatrix = findCaesarMatrix(matrixDefinitionData);
      // 提取所有的con terminals
      List<VisualEditTerminal> conTerminals = new ArrayList<>();
      for (VisualEditTerminal terminal : caesarMatrix.getAllOnlineTerminalChild()) {
        if (terminal instanceof CaesarConTerminal
            && ((CaesarConTerminal) terminal).getConsoleData() != null
            && !((CaesarConTerminal) terminal).getConsoleData().isStatusVp6()) {
          conTerminals.add(terminal);
        }
      }

      // 选择要连接的RX
      VisualEditTerminal selectedRx =
          ConnectDialog.show(
              graphContainer.getScene().getWindow(), tx.getName(), mode, conTerminals);
      if (selectedRx != null) {
        Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> pairs =
            caesarMatrix.getChildConnectorPair(selectedRx);
        // 做实际的连接操作
        if (!pairs.isEmpty()) {
          super.connect(tx, txConnector, selectedRx, pairs.iterator().next().getValue(), mode, rxChannel);
        } else {
          log.error("Fail to find the connected connector for {}!", selectedRx.getName());
        }
      }
    } else {
      super.connect(tx, txConnector, rx, rxConnector, mode, rxChannel);
    }
  }

  @Override
  public boolean isConnectable(
      VisualEditTerminal rx, Object rxConnector, VisualEditTerminal tx, Object txConnector) {
    if (rx.getParent() instanceof VpGroup) {
      return false;
    }

    CaesarDeviceController controller = deviceController.get();
    if (controller != null && !controller.getCaesarUserRight().isDeviceConnectable()) {
      return false;
    }

    return super.isConnectable(rx, rxConnector, tx, txConnector)
        && !getConnectableType(rx, rxConnector, tx, txConnector).isEmpty();
  }

  @Override
  public void setHighLightLinks(VisualEditNode... nodes) {
    if (!(currentGraph.get() instanceof SystemEditMatrixWrapper)) {
      return;
    }
    SystemEditMatrixWrapper matrixWrapper = (SystemEditMatrixWrapper) currentGraph.get();
    Set<VisualEditNode> highLightNodes = new HashSet<>(Arrays.asList(nodes));
    VisualEditMatrix matrix = matrixWrapper.getMatrix();
    if (matrix.getCellObject() == null) {
      // 还没有创建graph
      return;
    }
    ObservableList<VisualEditTerminal> allTerminals = matrix.getAllTerminalChild();
    Map<CaesarGridLineTerminal, Collection<Integer>> gridline2port = new HashMap<>();
    Map<Integer, CaesarGridLineTerminal> port2gridline = new HashMap<>();
    // 搜集所有gridline对应的端口
    for (VisualEditTerminal terminal : allTerminals) {
      if (terminal instanceof CaesarGridLineTerminal) {
        CaesarGridLineTerminal gridLine = (CaesarGridLineTerminal) terminal;
        PortData portData = gridLine.getPortData();
        if (portData == null) {
          continue;
        }
        PortData remotePort =
            getDeviceController()
                .getDataModel()
                .getConfigDataManager()
                .getPortData(portData.getType() - 1);
        int localExtenderPort = portData.getOutput();
        int remoteExtenderPort = 0;
        if (remotePort != null) {
          remoteExtenderPort = remotePort.getOutput();
        }

        Set<Integer> ports = new HashSet<>();
        if (localExtenderPort > 0) {
          port2gridline.put(localExtenderPort, gridLine);
          ports.add(localExtenderPort);
        }
        if (remoteExtenderPort > 0) {
          port2gridline.put(remoteExtenderPort, gridLine);
          ports.add(remoteExtenderPort);
        }
        if (!ports.isEmpty()) {
          gridline2port.put(gridLine, ports);
        }
      }
    }
    // 查找相关的gridline与terminal
    Set<VisualEditTerminal> targetTerminals = new HashSet<>();
    for (VisualEditNode node : nodes) {
      if (node instanceof VisualEditMatrix) {
        continue;
      }
      for (VisualEditTerminal terminal : node.getAllTerminalChild()) {
        targetTerminals.add(terminal);
        targetTerminals.addAll(systemEditModel.getConnectedTerminal(terminal));
      }
    }
    for (VisualEditTerminal terminal : targetTerminals) {
      if (terminal instanceof CaesarTerminalBase) {
        // 查找对应的gridline
        CaesarTerminalBase terminalBase = (CaesarTerminalBase) terminal;
        ExtenderData extenderData = terminalBase.getExtenderData();
        if (extenderData == null) {
          continue;
        }
        CaesarGridLineTerminal gridline = port2gridline.get(extenderData.getPort());
        if (gridline == null) {
          gridline = port2gridline.get(extenderData.getRdPort());
        }

        if (gridline != null) {
          highLightNodes.add(gridline);
        }
      } else if (gridline2port.containsKey(terminal)) {
        // 查找对应的外设
        for (Integer port : gridline2port.get(terminal)) {
          PortData portData =
              getDeviceController().getDataModel().getConfigData().getPortData(port - 1);
          if (portData == null || portData.getExtenderData() == null) {
            continue;
          }
          CaesarTerminalBase terminalBase =
              CaesarDataUtility.findTerminal(getModel(), portData.getExtenderData());
          if (terminalBase == null) {
            continue;
          }
          highLightNodes.add(terminalBase);
        }
      }
    }

    Collection<GraphObject> graphObjects =
        SystemEditHighLightHelper.getHighLightGraphs(
            this, systemEditModel, highLightNodes.toArray(new VisualEditNode[0]));
    graphObjects.retainAll(matrixWrapper.getMatrix().getCellObject().getConnectors());
    setHighLighter(graphObjects.toArray(new GraphObject[0]));
  }

  @Override
  public Collection<Color> getConnectionModeColor(String mode) {

    SwitchType type = SwitchType.valueOf(mode);

    Color color = null;
    switch (type) {
      case FULL:
        color = FULL_ACCESS_COLOR;
        break;
      case PRIVATE:
        color = PRIVATE_ACCESS_COLOR;
        break;
      case VIDEO:
        color = VIDEO_ACCESS_COLOR;
        break;
      default:
        //todo
        break;
    }
    if (color == null) {
      return Collections.emptyList();
    } else {
      return Collections.singletonList(color);
    }
  }

  @Override
  protected SystemEditGraph createSystemEditGraph() {
    return new CaesarSystemEditGraph(this);
  }

  @Override
  public boolean isMovable(VisualEditNode from, VisualEditNode to) {
    // VPCON不能动
    if (from instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) from;
      if (conTerminal.isVp()) {
        return false;
      }
    }

    if (to instanceof CaesarVideoWallFunc) {
      return from instanceof VpGroup;
    }
    if (to instanceof VpGroup) {
      return false;
    }

    // 跨屏组内的内容不能拖出来
    if (from.getParent() instanceof CaesarCrossScreenFunc) {
      return false;
    }
    if (to instanceof CaesarCrossScreenFunc && !((CaesarCrossScreenFunc) to).canAddChild()) {
      return false;
    }
    // 异形跨屏组的内容不能增删
    if (from.getParent() instanceof CaesarIrregularCrossScreenFunc) {
      return false;
    }
    if (to instanceof CaesarIrregularCrossScreenFunc) {
      return false;
    }
    if (to instanceof CpuGroup && from instanceof CpuGroup) {
      return false;
    }
    return super.isMovable(from, to);
  }

  @Override
  protected void createContextMenuItems(ContextMenu menu) {
    Collection<VisualEditNode> selectedNodes = getSelectedNodes();
    if (selectedNodes.isEmpty()) {
      return;
    }
    if (selectedNodes.size() == 1
        && selectedNodes.iterator().next() instanceof CaesarMatrix
        && currentGraph.get() instanceof SystemEditMatrixWrapper) {
      menu.getItems()
          .add(
              new MenuUsb(
                  this,
                  CaesarDataUtility.findCaesarMatrix(
                      ((SystemEditMatrixWrapper) currentGraph.get()).getMatrix()),
                  dataConverter,
                  getDeviceController()));
      if (getDeviceController().getCaesarUserRight().isTxGroupCreateDeletable()) {
        menu.getItems().add(new MenuGroupingTx4Caesar(this)); // 创建TX分组
      }
      menu.getItems().add(new MenuGroupingRx4Caesar(this)); // 创建RX分组
      if (getDeviceController().getCaesarUserRight().isMultiScreenCreateDeletable()) {
        menu.getItems().add(new MenuQuickCreateMultiScreen(this)); // 创建预案组
      }
      if (getDeviceController().getCaesarUserRight().isCrossScreenCreateDeletable()) {
        // 创建跨屏组
        menu.getItems().add(new MenuQuickCreateCrossScreen(this, getDeviceController()));
      }
      // 只能主机(非从主机)能创建视频墙.
      if (getDeviceController().getCaesarUserRight().isVideoWallCreateDeletable()) {
        // 创建视频墙组
        menu.getItems()
            .add(new MenuQuickCreateVideoWall(this, dataConverter.getVideoWallFuncManager()));
      }
    } else if (selectedNodes.size() == 1
        && (selectedNodes.iterator().next() instanceof CaesarUsbTxTerminal
        || selectedNodes.iterator().next() instanceof CaesarUsbRxTerminal)) {
      menu.getItems()
          .add(
              new MenuDelete4Caesar(
                  this, getDeviceController(), dataConverter.getVideoWallFuncManager()));
      menu.getItems().add(new MenuGroup(this));
      menu.getItems()
          .add(
              new MenuUsbRebind(
                  this,
                  CaesarDataUtility.findCaesarMatrix(
                      ((SystemEditMatrixWrapper) currentGraph.get()).getMatrix()),
                  getDeviceController()));
      menu.getItems().add(new MenuDeviceType(this));
    } else {
      if (getDeviceController().getCaesarUserRight().isDeviceConnectable()) {
        menu.getItems().add(new MenuDisconnect(this, getDeviceController()));
      }
      if (selectedNodes.stream()
          .filter(
              node ->
                  !(node instanceof VideoWallFunc)
                      || getDeviceController().getCaesarUserRight().isVideoWallCreateDeletable())
          .filter(
              node ->
                  !(node instanceof CrossScreenFunc)
                      || getDeviceController().getCaesarUserRight().isCrossScreenCreateDeletable())
          .anyMatch(
              node ->
                  !(node instanceof MultiScreenFunc)
                      || getDeviceController()
                      .getCaesarUserRight()
                      .isMultiScreenCreateDeletable())) {
        menu.getItems()
            .add(
                new MenuDelete4Caesar(
                    this, getDeviceController(), dataConverter.getVideoWallFuncManager()));
      }
      menu.getItems().add(new MenuGroup4Caesar(this));
      menu.getItems().add(new MenuAudioGroup4Caesar(this));
      // 只能主机(非从主机)能创建视频墙.
      if (getDeviceController().getCaesarUserRight().isVideoWallCreateDeletable()) {
        menu.getItems()
            .add(new MenuVideoWall4Caesar(this, dataConverter.getVideoWallFuncManager()));
        menu.getItems()
            .add(new MenuVideoWall4CaesarReuse(this, getDeviceController().getDataModel(),
                dataConverter.getVideoWallFuncManager(),
                CaesarDataUtility.findCaesarMatrix(
                    ((SystemEditMatrixWrapper) currentGraph.get()).getMatrix())));
      }
      // 从矩阵不能创建跨屏组与预案组
      if (getDeviceController().getCaesarUserRight().isCrossScreenCreateDeletable()) {
        menu.getItems().add(new MenuCrossScreen4Caesar(this, getDeviceController()));
        if (getDeviceController()
            .getDataModel()
            .getConfigMetaData()
            .getUtilVersion()
            .hasIrregularCrossScreen()) {
          menu.getItems().add(new MenuIrregularCrossScreen4Caesar(this, getDeviceController()));
        }
      }
      if (getDeviceController().getCaesarUserRight().isMultiScreenCreateDeletable()) {
        menu.getItems().add(new MenuMultiScreen4Caesar(this));
      }
      menu.getItems().add(new MenuDeviceType(this));
    }
  }

  @Override
  public void moveTo(VisualEditNode from, VisualEditNode to) {
    if (from instanceof CaesarCpuTerminal) {
      CaesarCpuTerminal terminal = (CaesarCpuTerminal) from;
      int toIndex;
      if (to instanceof CpuGroup) {
        // cpuTerminal移至CpuGroup
        CpuGroup cpuGroup = (CpuGroup) to;
        toIndex = cpuGroup.getTxRxGroupData().getOid() + 1;
      } else {
        // cpuTerminal移至非CpuGroup
        toIndex = 0;
      }
      getDeviceController()
          .execute(
              () -> {
                terminal.getCpuData().setTxGroupIndex(toIndex);
                getDeviceController().sendCpuData(Collections.singletonList(terminal.getCpuData()));
              });
      if (terminal.getParent() instanceof CpuGroup) {
        // 如果父节点为CpuGroup时，检查AllCpuTerminals(除了自己)有没有属于该父CpuGroup下(包括在线和离线),没有则删除
        CpuGroup parent = (CpuGroup) terminal.getParent();
        boolean anyMatch =
            getModel().getAllTerminals().stream()
                .anyMatch(
                    item ->
                        item instanceof CaesarCpuTerminal
                            && item != terminal
                            && ((CaesarCpuTerminal) item).getCpuData().getTxGroupIndex()
                            == parent.getTxRxGroupData().getOid() + 1);
        if (!anyMatch) {
          getDeviceController()
              .execute(() -> getDeviceController().deleteTxRxGroup(parent.getTxRxGroupData()));
        }
      }
    }
    super.moveTo(from, to);
  }

  @Override
  public boolean isDeletable(VisualEditNode node) {
    if (node.getClass().equals(VpGroup.class)) {
      return false;
    }
    if (node instanceof CaesarUsbTxTerminal) {
      return true;
    }
    if (node instanceof CaesarUsbRxTerminal) {
      return true;
    }
    if (node instanceof CaesarGridLineTerminal) {
      return false;
    }
    return super.isDeletable(node);
  }

  @Override
  public boolean isFixDeviceType(VisualEditNode node) {
    if (node instanceof CaesarGridLineTerminal) {
      return true;
    } else {
      return super.isFixDeviceType(node);
    }
  }

  @Override
  protected void onRefresh(ActionEvent event) {
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    if (applicationBase == null) {
      log.warn("Fail to find applicationbase!");
      return;
    }
    applicationBase.getTaskManager().addForegroundTask(new Task<Void>() {
      @Override
      protected Void call() {
        updateMessage(CaesarI18nCommonResource.getString("page.refreshing"));
        CaesarDeviceController deviceController = getDeviceController();
        try {
          deviceController.submit(
                  () -> ReloadUtility.refreshData(CaesarSystemEditController.this, deviceController))
              .get();
        } catch (ExecutionException | InterruptedException exception) {
          log.warn("Fail to refresh.", exception);
        }
        return null;
      }
    });
  }

  @Override
  public void close() {
    if (reloadConnectionScheduledFuture != null) {
      reloadConnectionScheduledFuture.cancel(true);
      reloadConnectionScheduledFuture = null;
    }
    super.close();
    setEntity(null);
  }

  private FileDialogue createFileDialogue(String[] extension) {
    FileDialogueFactory factory =
        InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);

    // Determine dialog type from extension description
    String dialogType = extension.length > 0
        ? FileDialogueManager.getDialogTypeFromExtension(extension[0]) : "DEFAULT";

    FileDialogue fileDialogue = factory.createFileDialogue(dialogType);
    // fileDialogue.setInitialFileName();
    for (int i = 1; i < extension.length; i++) {
      fileDialogue.addExtensionFilter(new ExtensionFilter(extension[0], extension[i]));
    }
    return fileDialogue;
  }

  @FXML
  protected void onExportOpticalModuleInfo(ActionEvent event) {
    FileDialogue fileDialogue = createFileDialogue(EXCEL_EXTENSION);
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    File file = fileDialogue.showSaveDialog(applicationBase.getMainWindow());
    if (file != null) {
      // Save the selected directory for future use
      FileDialogueManager fileDialogueManager =
          InjectorProvider.getInjector().getInstance(FileDialogueManager.class);
      fileDialogueManager.handleFileDialogueResult(fileDialogue, file);
      applicationBase.getTaskManager().addForegroundTask(new Task<Object>() {
        @Override
        protected Object call() {
          CaesarDeviceController deviceController = getDeviceController();
          if (deviceController == null) {
            return null;
          }
          updateMessage(
              CaesarI18nCommonResource.getString("systemedit.exporting.optModule"));
          boolean result =
              CaesarExtenderExportHelper.exportOpticalModuleInfoImpl(
                  deviceController, file);
          PlatformUtility.runInFxThread(() -> {
            if (result) {
              ViewUtility.showAlert(
                  applicationBase.getMainWindow(),
                  CaesarI18nCommonResource.getString("systemedit.exportsuccess"),
                  AlertExType.INFORMATION);
            } else {
              ViewUtility.showAlert(
                  applicationBase.getMainWindow(),
                  CaesarI18nCommonResource.getString("systemedit.exporterror"),
                  AlertExType.WARNING);
            }
          });
          return null;
        }
      });
    }
  }

  @FXML
  protected void onImportExtenderInfo(ActionEvent event) {
    FileDialogue fileDialogue = createFileDialogue(CSV_EXTENSION);
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    File file = fileDialogue.showOpenDialog(applicationBase.getMainWindow());
    if (file != null) {
      applicationBase.getTaskManager().addForegroundTask(new Task<Object>() {
        @Override
        protected Object call() {
          CaesarDeviceController deviceController = getDeviceController();
          if (deviceController == null) {
            return null;
          }
          updateMessage(CaesarI18nCommonResource.getString("systemedit.importing"));
          ImportResult result =
              CaesarExtenderExportHelper.importExtenderInfoImpl(
                  systemEditModel, deviceController, file);
          PlatformUtility.runInFxThread(() -> {
            if (result == ImportResult.OK) {
              ViewUtility.showAlert(
                  applicationBase.getMainWindow(),
                  CaesarI18nCommonResource.getString("systemedit.importsuccess"),
                  CaesarI18nCommonResource.getString("systemedit.title.success"),
                  AlertExType.INFORMATION);
            } else {
              ViewUtility.showAlert(
                  applicationBase.getMainWindow(),
                  CaesarI18nCommonResource.getString("systemedit.importerror")
                      + "\n"
                      + CaesarExtenderExportHelper.getErrorMessage(result),
                  CaesarI18nCommonResource.getString("systemedit.title.fail"),
                  AlertExType.WARNING);
            }
          });
          return null;
        }
      });
    }
  }

  @FXML
  protected void onExportExtenderInfo(ActionEvent event) {
    FileDialogue fileDialogue = createFileDialogue(CSV_EXTENSION);
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    File file = fileDialogue.showSaveDialog(applicationBase.getMainWindow());
    if (file != null) {
      applicationBase
          .getTaskManager()
          .addForegroundTask(
              new Task<Object>() {
                @Override
                protected Object call() {
                  updateMessage(CaesarI18nCommonResource.getString("systemedit.exporting"));
                  boolean result =
                      CaesarExtenderExportHelper.exportExtenderInfoImpl(systemEditModel, file);
                  PlatformUtility.runInFxThread(
                      () -> {
                        if (result) {
                          ViewUtility.showAlert(
                              applicationBase.getMainWindow(),
                              CaesarI18nCommonResource.getString("systemedit.exportsuccess"),
                              AlertExType.INFORMATION);
                        } else {
                          ViewUtility.showAlert(
                              applicationBase.getMainWindow(),
                              CaesarI18nCommonResource.getString("systemedit.exporterror"),
                              AlertExType.WARNING);
                        }
                      });
                  return null;
                }
              });
    }
  }

  private void updateGridLineStatus() {
    for (VisualEditTerminal terminal : getModel().getAllTerminals()) {
      if (terminal instanceof CaesarGridLineTerminal) {
        CaesarGridLineTerminal gridLineTerminal = (CaesarGridLineTerminal) terminal;
        gridLineTerminal.updateStatus();
      }
    }
  }

  protected CaesarMatrix findCaesarMatrix(MatrixDefinitionData matrixData) {
    CaesarMatrix matrix = CaesarDataUtility.findCaesarMatrix(systemEditModel, matrixData);
    matrix.setName(createMatrixName(matrixData));
    return matrix;
  }

  private String createMatrixName(MatrixDefinitionData data) {
    if (getDeviceController()
        .getDataModel()
        .getConfigData()
        .getSystemConfigData()
        .isMatrixGridEnabled()
        && !getDeviceController().getDataModel().isOnlyConfig()) {
      String masterStr = CaesarI18nCommonResource.getString("systemedit.master");
      String slaveStr = CaesarI18nCommonResource.getString("systemedit.slave");
      String format = "{0}({1})";
      if (Utilities.isMatrixMaster(getDeviceController().getDataModel(), data)) {
        return MessageFormat.format(format, data.getDevice(), masterStr);
      } else {
        return MessageFormat.format(format, data.getDevice(), slaveStr);
      }
    } else {
      return data.getDevice();
    }
  }

  private void refresh() {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    Task<Void> task = new Task<Void>() {
      @Override
      protected Void call() {
        updateMessage(CaesarI18nCommonResource.getString("page.refreshing"));
        try {
          Thread.sleep(1000);
        } catch (InterruptedException ex) {
          log.error("" + ex);
        }
        for (Page page : entity.getPages()) {
          if (page.getVisibleProperty().get()) {
            page.refresh();
          }
        }
        return null;
      }
    };
    app.getTaskManager().addForegroundTask(task);
  }
}
