package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import lombok.Getter;
import lombok.experimental.Delegate;

/**
 * .
 */
public class CaesarCpuTerminalClipWrapper implements VisualEditTerminal {

  @Delegate(types = {VisualEditTerminal.class})
  @Getter
  private CaesarCpuTerminalWrapper terminalWrapper;

  @Getter
  private final int sourceClipIndex;

  public CaesarCpuTerminalClipWrapper(CaesarCpuTerminalWrapper terminalWrapper, int sourceClipIndex) {
    this.terminalWrapper = terminalWrapper;
    this.sourceClipIndex = sourceClipIndex;
  }

  @Override
  public int hashCode() {
    return terminalWrapper.hashCode() ^ sourceClipIndex;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof CaesarCpuTerminalClipWrapper) {
      CaesarCpuTerminalClipWrapper input = (CaesarCpuTerminalClipWrapper) obj;
      return input.terminalWrapper.equals(terminalWrapper) && input.sourceClipIndex == sourceClipIndex;
    } else {
      return false;
    }
  }
}
