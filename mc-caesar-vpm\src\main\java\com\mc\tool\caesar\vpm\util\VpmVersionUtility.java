package com.mc.tool.caesar.vpm.util;

import com.google.common.primitives.Ints;
import com.mc.tool.caesar.Versions;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import java.util.Locale;
import lombok.Getter;

/**
 * 版本工具类.
 * 用于解析和生成VPM版本信息.
 */
public class VpmVersionUtility {

  /**
   * VPM版本不匹配异常.
   */
  @Getter
  public static class VpmVersionMismatchException extends RuntimeException {
    public VpmVersionMismatchException(String message, VersionDef version) {
      super(message);
      this.version = version;
    }

    private final transient VersionDef version;
  }

  /**
   * 将VersionDef转换为VPM版本字符串.
   * 格式为 "主版本.子版本.修订版本-限定符"
   * 例如 "1.2.3-beta4"
   */
  public static String toVpmVersionString(VersionDef version) {
    if (version == null) {
      return "0.0.0";
    }
    StringBuilder sb = new StringBuilder();
    sb.append(version.getMasterVersion()).append('.');
    sb.append((version.getSubVersion() >> 8) & 0xFF).append('.');
    sb.append(version.getSubVersion() & 0xFF);
    if (version.getYear() != 3) {
      sb.append('-');
      switch (version.getYear()) {
        case 0:
          sb.append("alpha");
          break;
        case 1:
          sb.append("beta");
          break;
        case 2:
          sb.append("rc");
          break;
        default:
          sb.append("unknown");
      }
      sb.append(version.getMonth());
    }
    return sb.toString();
  }

  /**
   * 获取当前VPM的版本信息.
   * Year: 0 - beta, 1 - beta, 2 - rc, 3 - 正式版
   * Month: build number
   */
  public static VersionDef makeVpmVersion() {
    VersionDef version = new VersionDef();
    version.setHasPatchVersion(true);
    Integer main = Ints.tryParse(Versions.MAJOR_VERSION);
    Integer minor = Ints.tryParse(Versions.MINOR_VERSION);
    Integer patch = Ints.tryParse(Versions.PATCH_VERSION);
    version.setMasterVersion(main != null ? main : 0);
    int subVersion = minor != null ? minor << 8 : 0;
    subVersion |= patch != null ? patch : 0;
    version.setSubVersion(subVersion);

    String qualifier = Versions.VERSION_QUALIFIER;
    // 处理版本限定符
    if (qualifier != null && !qualifier.isEmpty()) {
      // 使用正则表达式解析字符串前缀与数字部分
      String[] parts = qualifier.split("(?<=\\D)(?=\\d)");
      if (parts.length == 2) {
        try {
          int number = Integer.parseInt(parts[1]);
          parts[0] = parts[0].toLowerCase(Locale.ENGLISH);
          version.setMonth(number);
          switch (parts[0]) {
            case "beta":
              version.setYear(1);
              break;
            case "rc":
              version.setYear(2);
              break;
            default:
              version.setYear(0);
          }
        } catch (NumberFormatException e) {
          version.setYear(0);
        }
      }
    } else {
      version.setYear(3);
    }

    return version;

  }
}
