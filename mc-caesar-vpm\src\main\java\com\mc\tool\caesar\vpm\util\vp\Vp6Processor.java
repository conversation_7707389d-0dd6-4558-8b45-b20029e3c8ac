package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.vp.ColorUtility;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData.Vp6ScalerData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javafx.geometry.Rectangle2D;
import javafx.util.Pair;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class Vp6Processor {

  static class DhdmiMapper {

    @Data
    static class Channel2Port {

      public int port2k;
      public int txId;
      public int channel;

      public Channel2Port(int port2k, int txId, int channel) {
        this.port2k = port2k;
        this.txId = txId;
        this.channel = channel;
      }
    }

    Set<Channel2Port> channels = new HashSet<>();

    /**
     * 添加dhdmi通道对应的端口信息.
     *
     * @param port2k  2k端口信息
     * @param txId    txId
     * @param channel dhdmi的通道号，0或1
     */
    public void addNewPort(int port2k, int txId, int channel) {
      List<Channel2Port> result = getMatchedChannel2Port(txId, channel);
      Channel2Port portMatched = null;
      for (Channel2Port item : result) {
        if (item.port2k == port2k) {
          portMatched = item;
          break;
        }
      }
      if (portMatched != null) {
        channels.remove(portMatched);
      } else {
        channels.add(new Channel2Port(port2k, txId, channel));
      }
    }

    /**
     * 匹配dhmi的端口.
     *
     * @param txId    txid
     * @param channel dhdmi的通道号，0或1
     * @return 如果匹配到，返回对应的2k端口号(0~3),否则返回-1
     */
    public int matchPort(int txId, int channel) {
      List<Channel2Port> result = getMatchedChannel2Port(txId, channel);
      if (result.size() > 0) {
        channels.remove(result.get(0));
        return result.get(0).port2k;
      } else {
        return -1;
      }
    }

    private List<Channel2Port> getMatchedChannel2Port(int txId, int channel) {
      List<Channel2Port> result = new ArrayList<>();
      for (Channel2Port item : channels) {
        if (item.txId == txId && item.channel == 1 - channel) {
          result.add(item);
          break;
        }
      }
      return result;
    }
  }

  /**
   * .
   */
  public enum VideoWallError {
    WINDOW_OVERFLOW,
    SCREEN_LAYER_OVERFLOW,
    DEVICE_LAYER_OVERFLOW
  }

  /**
   * 处理视频墙数据，生成配置信息与连接信息.
   *
   * @param matrix    矩阵
   * @param videoWall 视频墙
   */
  public static void processVideoWall(VpMatrix matrix, VpVideoWall videoWall, Vp6WallCurrCfg currCfg) {
    Collection<VpVideoDataWrapper> newVideoDatas = recreateVideos(matrix, videoWall, true);
    Collection<Collection<Integer>> rxids = groupVp6Rxids(matrix, videoWall);
    Map<Integer, Collection<VpVideoPortInfo>> oldPorts = new HashMap<>();
    for (Collection<Integer> ids : rxids) {
      int vpconId = getVpconId(ids.iterator().next());
      oldPorts.put(vpconId, currCfg.getVpconPortInfos(vpconId));
    }

    currCfg.clearOldData();
    // 对每一组做处理
    for (Collection<Integer> ids : rxids) {
      if (ids.isEmpty()) {
        continue;
      }
      int vpconId = getVpconId(ids.iterator().next());
      if (!matrix.isExtOnline(vpconId)) {
        continue;
      }

      // 提取相关视频窗口
      Collection<VpVideoDataWrapper> videoDatas = groupVideos(videoWall, newVideoDatas, ids);
      // 分配端口
      Collection<VpVideoDataWrapper> videosWithPort =
          distributeVp6Ports(matrix, vpconId, videoDatas, oldPorts.get(vpconId));
      // 过滤
      videosWithPort = filerVp6Videos(matrix, videosWithPort, vpconId);
      // 切割图层
      Collection<VpScreenLayers> layers = splitLayers(videoWall, videosWithPort, ids);
      // 过滤图层
      layers = filterVp6Layers(matrix, layers, vpconId);
      // 添加图层索引
      layers = updateClipIndex(layers);
      // 生成配置
      Vp6ConfigData configData = calculateVp6Config(matrix, videoWall, layers);
      // 保存配置
      Collection<VpVideoPortInfo> portInfos = createPortInfos(videoWall, videosWithPort);
      currCfg.setVpconPortInfos(vpconId, portInfos);
      currCfg.setVpConfigData(vpconId, configData);
    }
  }

  /**
   * .
   */
  public static Collection<Pair<Integer, VideoWallError>> checkVideoWall(
      VpMatrix matrix, VpVideoWall videoWall) {
    List<Pair<Integer, VideoWallError>> errors = new ArrayList<>();
    Collection<VpVideoDataWrapper> newVideoDatas = recreateVideos(matrix, videoWall, false);
    Collection<Collection<Integer>> rxids = groupVp6Rxids(matrix, videoWall);

    // 对每一组做处理
    for (Collection<Integer> ids : rxids) {
      if (ids.isEmpty()) {
        continue;
      }
      int vpconId = getVpconId(ids.iterator().next());

      // 提取相关视频窗口
      Collection<VpVideoDataWrapper> videoDatas = groupVideos(videoWall, newVideoDatas, ids);
      // 分配端口
      Collection<VpVideoDataWrapper> videosWithPort =
          distributeVp6Ports(matrix, vpconId, videoDatas, Collections.emptyList());
      int oldVideoSize = videosWithPort.size();
      // 过滤
      videosWithPort = filerVp6Videos(matrix, videosWithPort, vpconId);
      if (videosWithPort.size() < oldVideoSize) {
        errors.addAll(ids.stream()
            .map((item) -> new Pair<Integer, VideoWallError>(item, VideoWallError.WINDOW_OVERFLOW))
            .collect(Collectors.toList()));
        continue;
      }
      // 切割图层
      Collection<VpScreenLayers> layers = splitLayers(videoWall, videosWithPort, ids);
      int maxLayerPerScreen = Vp6Constants.LAYER_PER_PORT;
      int maxTotalLayer = Vp6Constants.MAX_TOTAL_LAYER;
      if (layers.stream().map((item) -> item.layers.size()).reduce(Integer::sum).get()
          > maxTotalLayer) {
        errors.addAll(
            ids.stream()
                .map((item) -> new Pair<>(item, VideoWallError.DEVICE_LAYER_OVERFLOW))
                .collect(Collectors.toList()));
        continue;
      }
      for (VpScreenLayers layer : layers) {
        if (layer.layers.size() > maxLayerPerScreen) {
          errors.add(new Pair<>(layer.rxId, VideoWallError.SCREEN_LAYER_OVERFLOW));
        }
      }
    }
    return errors;
  }

  /**
   * 重建视频窗口列表，去掉无效窗口，添加底图图层.
   *
   * @param matrix    矩阵
   * @param videoWall 视频墙
   * @return 新的视频窗口列表，保持原来的顺序
   */
  static Collection<VpVideoDataWrapper> recreateVideos(
      VpMatrix matrix, VpVideoWall videoWall, boolean filterEmptyWindow) {
    List<VpVideoDataWrapper> result = new ArrayList<>();
    if (videoWall.showBgImg() && videoWall.getBgImgWidth() > 0 && videoWall.getBgImgHeight() > 0) {
      Vp6BgImgVideoData videoData =
          new Vp6BgImgVideoData(videoWall.getTotalWidth(), videoWall.getTotalHeight(), 255);
      // 底图不能缩小，所以底图的分辨率不能比视频墙大
      int width = Math.min(videoWall.getTotalWidth(), videoWall.getBgImgWidth());
      int height = Math.min(videoWall.getTotalHeight(), videoWall.getBgImgHeight());
      VpResolution resolution =
          new VpResolution(width, height, width > 1920 ? VpResolution.Type.VIDEO_4K30 : VpResolution.Type.VIDEO_2K);
      result.add(new VpVideoDataWrapper(videoData, resolution, -1));
    }
    for (int i = 0; i < videoWall.getVideoCount(); i++) {
      VpVideoData data = videoWall.getVideoData(i);
      if (data == null) {
        continue;
      }
      VpResolution resolution = matrix.getTxCurrentResolution(data.getTxId(), data.getSourceIndex());
      VpTxClipData txClipData = matrix.getTxClipData(data.getTxId(), data.getSourceIndex(), data.getClipIndex());
      VpVideoDataWrapper videoDataWrapper =
          new VpVideoDataWrapper(data, resolution, txClipData, i, false);
      if (filterEmptyWindow) {
        // 去掉不在线窗口
        if (data.getTxId() <= 0 || !matrix.isExtOnline(data.getTxId())) {
          continue;
        }
        // 去掉分辨率异常窗口
        if (!videoDataWrapper.isValid()) {
          continue;
        }
      }
      result.add(videoDataWrapper);
    }
    return result;
  }

  /**
   * 对RX按照VP6分组.
   *
   * @param matrix    矩阵
   * @param videoWall 视频墙
   * @return 分组后的rx id.
   */
  static Collection<Collection<Integer>> groupVp6Rxids(VpMatrix matrix, VpVideoWall videoWall) {
    Set<Integer> ids = new HashSet<>();
    for (int i = 0; i < videoWall.getRows(); i++) {
      for (int j = 0; j < videoWall.getColumns(); j++) {
        VpScreenData screenData = videoWall.getScreenData(i, j);
        if (screenData == null || screenData.getRxId() == 0) {
          continue;
        }
        ids.add(screenData.getRxId());
      }
    }

    // 根据vpcon id分组
    return new ArrayList<>(
        ids.stream().collect(Collectors.groupingBy(Vp6Processor::getVpconId)).values());
  }

  /**
   * 根据rxid分组视频窗口.
   *
   * @param videoWall 视频墙
   * @param rxids     rx id集合.
   * @return 指定的rx的所对应的视频窗口，保持原来的顺序
   */
  static Collection<VpVideoDataWrapper> groupVideos(
      VpVideoWall videoWall, Collection<VpVideoDataWrapper> videoDatas, Collection<Integer> rxids) {
    Set<Integer> ids = new HashSet<>(rxids);
    List<VpVideoDataWrapper> result = new ArrayList<>();
    List<Rectangle2D> rxRects = new ArrayList<>();
    int top = 0;
    // 计算RX的空间
    for (int i = 0; i < videoWall.getRows(); i++) {
      int left = 0;
      for (int j = 0; j < videoWall.getColumns(); j++) {
        VpScreenData screenData = videoWall.getScreenData(i, j);
        if (ids.contains(screenData.getRxId())) {
          VpResolution res = screenData.getResolution();
          rxRects.add(
              new Rectangle2D(screenData.getXpos(), screenData.getYpos(), res.width, res.height));
        }
        left += videoWall.getColumnWidth(j);
      }
      top += videoWall.getRowHeight(i);
    }
    // 筛选与RX空间有交集的Video
    for (VpVideoDataWrapper videoData : videoDatas) {
      Rectangle2D txRect =
          new Rectangle2D(
              videoData.getLeft(), videoData.getTop(), videoData.getWidth(), videoData.getHeight());
      for (Rectangle2D rxRect : rxRects) {
        if (txRect.intersects(rxRect)) {
          result.add(videoData);
          break;
        }
      }
    }
    return result;
  }

  /**
   * 筛选可显示的视频窗口.
   *
   * @param videos  视频窗口与其端口信息
   * @param vpconid vpcon id
   * @return 筛选后的视频窗口，保持原来的顺序
   */
  static Collection<VpVideoDataWrapper> filerVp6Videos(
      VpMatrix matrix, Collection<VpVideoDataWrapper> videos, int vpconid) {
    // 去掉没有端口信息的窗口
    List<VpVideoDataWrapper> result = new ArrayList<>();
    for (VpVideoDataWrapper wrapper : videos) {
      if (wrapper.getPort() >= 0) {
        result.add(wrapper);
      }
    }
    return result;
  }

  /**
   * 分配端口.
   *
   * @param matrix       矩阵
   * @param videos       需要分配端口的视频窗口.
   * @param oldPortInfos 旧的端口分配信息，如果没有旧的分配信息，为空列表
   * @return 各个窗口的端口
   */
  static Collection<VpVideoDataWrapper> distributeVp6Ports(
      VpMatrix matrix,
      int vpconId,
      Collection<VpVideoDataWrapper> videos,
      Collection<VpVideoPortInfo> oldPortInfos) {
    int portCnt = Vp6Constants.IN_PORT_COUNT;
    boolean[] usablePorts = new boolean[portCnt];
    boolean[] is2kPorts = new boolean[portCnt]; // 对应的端口是否连接2k信号
    boolean[] onlinePorts = new boolean[portCnt];
    for (int i = 0; i < portCnt; i++) {
      usablePorts[i] = isVpconPortOnline(matrix, vpconId, i);
      onlinePorts[i] = usablePorts[i];
    }

    Map<VideoRect, Integer> oldRectPorts = makePortInfoMap(oldPortInfos);
    // 已经匹配到的dhdmi窗口，只用于查找旧端口
    Map<Integer, VpVideoDataWrapper> matchedDhdmiVideos = new HashMap<>();
    // 用于匹配dhdmi端口
    DhdmiMapper dhdmiMapper = new DhdmiMapper();
    List<VpVideoDataWrapper> videoList = new ArrayList<>(videos);
    List<VpVideoDataWrapper> result = new ArrayList<>();
    // 重置端口
    for (VpVideoDataWrapper wrapper : videoList) {
      wrapper.setPort(-1);
    }
    // 使用旧的端口
    for (VpVideoDataWrapper videoData : videoList) {
      VideoRect rect =
          new VideoRect(
              videoData.getTxId(),
              videoData.getSourceIndex(),
              videoData.getLeft(),
              videoData.getTop(),
              videoData.getWidth(),
              videoData.getHeight());
      if (!oldRectPorts.containsKey(rect)) {
        continue;
      }
      int port = oldRectPorts.get(rect);
      if (port >= Vp6Constants.BGIMG_PORT) {
        continue;
      }
      VpResolution resolution = videoData.getResolution();
      if (resolution.is4k() || matrix.hasDualInput(videoData.getTxId())) {
        // 保证两个端口都可用
        // 或者双dhdmi下，另外一端口已经匹配上
        int port4k = get4kPort(port);

        if (port < Vp6ConfigData.IN_PORT_4K_COUNT && !onlinePorts[port4k]
            || usablePorts[port4k]
            || matrix.hasDualInput(videoData.getTxId())
            && matchedDhdmiVideos.containsKey(port4k)
            && matchedDhdmiVideos.get(port4k).getTxId() == videoData.getTxId()) {
          videoData.setPort(port);
          usablePorts[port] = false;
          usablePorts[port4k] = false;
          if (matrix.hasDualInput(videoData.getTxId())) {
            matchedDhdmiVideos.put(port, videoData);
            dhdmiMapper.addNewPort(
                port < Vp6ConfigData.IN_PORT_4K_COUNT ? port : port4k,
                videoData.getTxId(),
                videoData.getSourceIndex());
          }
        }
      } else if (usablePorts[port]) {
        videoData.setPort(port);
        usablePorts[port] = false;
        is2kPorts[port] = true;
      }
    }
    // 分配新的端口
    int undistributed4kVideos =
        distributePortsImpl(matrix, usablePorts, is2kPorts, onlinePorts, videoList, dhdmiMapper);

    if (undistributed4kVideos > 0) {
      Map<Integer, VpVideoDataWrapper> portMap2kVideo = new HashMap<>();
      for (VpVideoDataWrapper video : videoList) {
        if (video.getPort() >= 0 && video.getPort() < is2kPorts.length
            && is2kPorts[video.getPort()]) {
          portMap2kVideo.put(video.getPort(), video);
        }
      }
      // 检查是否有端口可以压缩出来连接4k
      List<Integer> fullFree2kPorts = new ArrayList<>(); // 两个端口(i, i + 4)都在线，其中一个为2k，一个没有用
      List<Integer> halfFree2kPorts = new ArrayList<>(); // 第一个端口i在线，i+4端口不在线，第一个端口为2k
      for (int i = 0; i < portCnt / 2; i++) {
        if (is2kPorts[i] && usablePorts[get4kPort(i)]
            || usablePorts[i] && is2kPorts[get4kPort(i)]) {
          fullFree2kPorts.add(i);
        }
        if (is2kPorts[i] && !onlinePorts[get4kPort(i)]) {
          halfFree2kPorts.add(i);
        }
      }
      // 移动halfFree的2k端口到fullFree的2k端口
      int halfFreeCnt = Math.min(halfFree2kPorts.size(), fullFree2kPorts.size());
      int hasFreeCnt = 0; // 已释放出来的4k端口个数
      for (int i = 0; i < halfFreeCnt && hasFreeCnt < undistributed4kVideos; i++) {
        int sourcePort = halfFree2kPorts.get(i);
        int targetPort = fullFree2kPorts.get(i);
        if (is2kPorts[targetPort] && usablePorts[get4kPort(targetPort)]) {
          targetPort = get4kPort(targetPort);
        }
        movePort(usablePorts, is2kPorts, portMap2kVideo, sourcePort, targetPort);
        hasFreeCnt++;
      }
      // 合并剩下的fullFree的2k端口
      for (int i = halfFreeCnt;
           i < fullFree2kPorts.size() && hasFreeCnt < undistributed4kVideos;
           i += 2) {
        if (i + 1 >= fullFree2kPorts.size()) {
          break;
        }
        int sourcePort = fullFree2kPorts.get(i);
        int targetPort = fullFree2kPorts.get(i + 1);
        // 选择不空的端口
        if (usablePorts[sourcePort] && is2kPorts[get4kPort(sourcePort)]) {
          sourcePort = get4kPort(sourcePort);
        }
        // 选择空的端口
        if (is2kPorts[targetPort] && usablePorts[get4kPort(targetPort)]) {
          targetPort = get4kPort(targetPort);
        }
        movePort(usablePorts, is2kPorts, portMap2kVideo, sourcePort, targetPort);
        hasFreeCnt++;
      }
      // 重新分配端口
      if (hasFreeCnt > 0) {
        distributePortsImpl(matrix, usablePorts, is2kPorts, onlinePorts, videoList, dhdmiMapper);
      }
    }

    // 整理顺序
    for (VpVideoDataWrapper videoData : videoList) {
      VpResolution resolution = videoData.getResolution();
      // 切割4k窗口
      if (resolution.is4k()) {
        result.addAll(split4kVideos(videoData));
      } else {
        result.add(videoData);
      }
    }
    return result;
  }

  /**
   * 把端口为sourcePort的2k窗口移动到端口targetPort.
   *
   * @param usablePorts    可用的端口
   * @param is2kPorts      2k端口
   * @param portMap2kVideo 端口与2k视频窗口的对应关系
   * @param sourcePort     源端口
   * @param targetPort     目标端口
   */
  private static void movePort(
      boolean[] usablePorts,
      boolean[] is2kPorts,
      Map<Integer, VpVideoDataWrapper> portMap2kVideo,
      int sourcePort,
      int targetPort) {
    VpVideoDataWrapper sourceVideo = portMap2kVideo.get(sourcePort);
    sourceVideo.setPort(targetPort);
    usablePorts[sourcePort] = true;
    usablePorts[targetPort] = false;
    is2kPorts[sourcePort] = false;
    is2kPorts[targetPort] = true;
    portMap2kVideo.remove(sourcePort);
    portMap2kVideo.put(targetPort, sourceVideo);
  }

  /**
   * 分配新端口.
   *
   * @param matrix      matrix
   * @param usablePorts 可用的端口
   * @param is2kPorts   已分配的2k端口
   * @param onlinePorts 在线的端口
   * @param videoList   视频窗口列表
   * @return 没有分配到的4k/dhdmi视频窗口个数
   */
  private static int distributePortsImpl(
      VpMatrix matrix,
      boolean[] usablePorts,
      boolean[] is2kPorts,
      boolean[] onlinePorts,
      List<VpVideoDataWrapper> videoList,
      DhdmiMapper dhdmiMapper) {
    int undistributed4kVideos = 0;
    for (VpVideoDataWrapper videoData : videoList) {
      if (videoData.getPort() >= 0) {
        continue;
      }
      VpResolution resolution = videoData.getResolution();
      if (videoData.getTxId() == Vp6Constants.BGIMG_TX_ID) {
        // 底图
        videoData.setPort(Vp6Constants.BGIMG_PORT);
      } else if (matrix.hasDualInput(videoData.getTxId())) {
        // dhdmi，占用两个端口
        // 查找端口
        int port = dhdmiMapper.matchPort(videoData.getTxId(), videoData.getSourceIndex());
        if (port == -1) {
          port = findUsable4kPort(usablePorts, onlinePorts);
          if (port < 0) {
            videoData.setPort(port);
            undistributed4kVideos++;
            continue;
          }
          dhdmiMapper.addNewPort(port, videoData.getTxId(), videoData.getSourceIndex());
        }
        int port4k = get4kPort(port);
        videoData.setPort(videoData.getSourceIndex() <= 0 ? port : port4k);

        usablePorts[port] = false;
        usablePorts[get4kPort(port)] = false;
      } else if (resolution.is4k()) {
        // 4k，占用两个端口
        int port = findUsable4kPort(usablePorts, onlinePorts);
        if (port >= 0) {
          usablePorts[port] = false;
          usablePorts[get4kPort(port)] = false;
        } else {
          undistributed4kVideos++;
        }
        videoData.setPort(port);
      } else {
        // 普通2k，占用一个端口
        int port = findUsableSinglePort(usablePorts, onlinePorts);
        if (port >= 0) {
          videoData.setPort(port);
          usablePorts[port] = false;
          is2kPorts[port] = true;
        }
      }
    }
    return undistributed4kVideos;
  }

  /**
   * 切割4k窗口.
   *
   * @param video4k 4k窗口
   * @return 切割后的窗口
   */
  private static Collection<VpVideoDataWrapper> split4kVideos(VpVideoDataWrapper video4k) {
    VpResolution resolution = video4k.getResolution();
    VpTxClipData txClipData = video4k.getTxClipData();
    // 分成左右两半
    final int leftInWidth = Vp6Constants.WIDTH_2K;
    final int rightInWidth = resolution.width - leftInWidth;
    final int clippedLeftInWidth = Math.max(leftInWidth - txClipData.getLeft()
        - Math.max(0, txClipData.getRight() - rightInWidth), 0);  // 裁剪后左边的未缩放的宽度
    // 4舍5入
    final int leftOutWidth =
        (int) (video4k.getWidth() * clippedLeftInWidth * 1.0 / video4k.getClippedResolutionWidth()
            + 0.5);

    final int rightOutWidth = video4k.getWidth() - leftOutWidth;
    final VpSimpleVideoData leftVideo =
        new VpSimpleVideoData(
            video4k.getLeft(),
            video4k.getTop(),
            leftOutWidth,
            video4k.getHeight(),
            video4k.getAlpha(),
            video4k.getTxId());
    VpResolution leftResolution = new VpResolution(leftInWidth, resolution.height, VpResolution.Type.VIDEO_2K);

    VpTxClipData leftTxClipData =
        new VpTxClipData(txClipData.getLeft(), txClipData.getTop(), Math.max(0, txClipData.getRight() - rightInWidth),
            txClipData.getBottom());

    VpVideoDataWrapper leftVideoWrapper =
        new VpVideoDataWrapper(leftVideo, leftResolution, leftTxClipData, video4k.getIndex(), true);
    leftVideoWrapper.setPort(video4k.getPort());

    final VpSimpleVideoData rightVideo =
        new VpSimpleVideoData(
            video4k.getLeft() + leftOutWidth,
            video4k.getTop(),
            rightOutWidth,
            video4k.getHeight(),
            video4k.getAlpha(),
            video4k.getTxId());
    VpResolution rightResolution = new VpResolution(rightInWidth, resolution.height, VpResolution.Type.VIDEO_2K);

    VpTxClipData rightTxClipData = new VpTxClipData(Math.max(0, txClipData.getLeft() - leftInWidth), txClipData.getTop(),
        txClipData.getRight(), txClipData.getBottom());

    VpVideoDataWrapper rightVideoWrapper =
        new VpVideoDataWrapper(rightVideo, rightResolution, rightTxClipData, video4k.getIndex(),
            true);
    if (video4k.getPort() >= 0) {
      rightVideoWrapper.setPort(get4kPort(video4k.getPort()));
    } else {
      rightVideoWrapper.setPort(-1);
    }
    List<VpVideoDataWrapper> result = new ArrayList<>();
    result.add(leftVideoWrapper);
    result.add(rightVideoWrapper);
    return result;
  }

  static int findUsableSinglePort(boolean[] usablePorts, boolean[] onlinePorts) {
    int candidate = -1;
    for (int i = 0; i < usablePorts.length / 2; i++) {
      int port4k = get4kPort(i);
      // 优先1：两个端口都在线，其中一个被使用了
      // 优先2：第一个端口不在线，第二个端口在线
      if (onlinePorts[i] && onlinePorts[port4k] && usablePorts[i] && !usablePorts[port4k]) {
        return i;
      }
      if (onlinePorts[i] && onlinePorts[port4k] && !usablePorts[i] && usablePorts[port4k]) {
        return port4k;
      }
      if (!onlinePorts[i] && onlinePorts[port4k] && usablePorts[port4k]) {
        return port4k;
      }
      // 其他情况作为候选
      if (candidate < 0) {
        if (usablePorts[i]) {
          candidate = i;
        } else if (usablePorts[port4k]) {
          candidate = port4k;
        }
      }
    }
    return candidate;
  }

  static int findUsable4kPort(boolean[] usablePorts, boolean[] onlinePorts) {
    // 优先选择第一个端口在线，第二个端口不在线的情况.
    int candidate = -1;
    for (int i = 0; i < usablePorts.length / 2; i++) {
      int port4k = get4kPort(i);
      if (usablePorts[i] && !onlinePorts[port4k]) {
        return i;
      }
      if (usablePorts[i] && usablePorts[port4k] && candidate < 0) {
        candidate = i;
      }
    }
    return candidate;
  }

  static int get4kPort(int port) {
    if (port >= Vp6Constants.BGIMG_PORT) {
      return port ^ 0x00000001;
    } else {
      if (port >= Vp6ConfigData.IN_PORT_4K_COUNT) {
        return port - Vp6ConfigData.IN_PORT_4K_COUNT;
      } else {
        return port + Vp6ConfigData.IN_PORT_4K_COUNT;
      }
    }
  }

  static Collection<VpVideoPortInfo> createPortInfos(
      VpVideoWall videoWall, Collection<VpVideoDataWrapper> videos) {
    return videos.stream()
        .map((item) -> makePortInfo(videoWall, item))
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  static VpVideoPortInfo makePortInfo(VpVideoWall videoWall, VpVideoDataWrapper videoData) {
    if (videoData.getIndex() < 0 || videoData.getIndex() >= videoWall.getVideoCount()) {
      return null;
    }
    // 对于4k窗口，只用前面的端口
    if (videoData.is4kSplit() && videoData.getPort() > get4kPort(videoData.getPort())) {
      return null;
    }
    VpVideoPortInfo info = new VpVideoPortInfo();
    VpVideoData rawVideoData = videoWall.getVideoData(videoData.getIndex());
    info.txId = rawVideoData.getTxId();
    info.txIndex = rawVideoData.getSourceIndex();
    info.left = rawVideoData.getLeft();
    info.top = rawVideoData.getTop();
    info.width = rawVideoData.getWidth();
    info.height = rawVideoData.getHeight();
    info.portIndex = videoData.getPort();
    return info;
  }

  static Map<VideoRect, Integer> makePortInfoMap(Collection<VpVideoPortInfo> infos) {
    Map<VideoRect, Integer> result = new HashMap<>();
    for (VpVideoPortInfo portInfo : infos) {
      result.put(
          new VideoRect(
              portInfo.txId,
              portInfo.txIndex,
              portInfo.left,
              portInfo.top,
              portInfo.width,
              portInfo.height),
          portInfo.portIndex);
    }
    return result;
  }

  /**
   * 切割图层.
   *
   * @param videoWall 视频墙
   * @param videos    要切割的窗口与其端口
   * @param rxids     屏幕rxid
   * @return 指定屏幕内的所有图层的集合
   */
  static Collection<VpScreenLayers> splitLayers(
      VpVideoWall videoWall, Collection<VpVideoDataWrapper> videos, Collection<Integer> rxids) {
    Map<Integer, Rectangle2D> rxRects = new HashMap<>();
    Map<Integer, VpScreenLayers> rxLayers = new TreeMap<>(); // 用treemap可以预测顺序

    Set<Integer> ids = new HashSet<>(rxids);
    int top = 0;
    // 计算RX的空间
    for (int i = 0; i < videoWall.getRows(); i++) {
      int left = 0;
      for (int j = 0; j < videoWall.getColumns(); j++) {
        VpScreenData screenData = videoWall.getScreenData(i, j);
        if (ids.contains(screenData.getRxId())) {
          VpResolution res = screenData.getResolution();
          Rectangle2D screenBounds = new Rectangle2D(screenData.getXpos(), screenData.getYpos(), res.width, res.height);
          rxRects.put(screenData.getRxId(), screenBounds);
          int oid = i * videoWall.getColumns() + j;
          VpScreenLayers layers = new VpScreenLayers();
          layers.rxId = screenData.getRxId();
          layers.oid = oid;
          layers.screenBounds = screenBounds;
          rxLayers.put(screenData.getRxId(), layers);
        }
        left += videoWall.getColumnWidth(j);
      }
      top += videoWall.getRowHeight(i);
    }
    for (VpVideoDataWrapper videoData : videos) {
      Rectangle2D txRect =
          new Rectangle2D(
              videoData.getLeft(), videoData.getTop(), videoData.getWidth(), videoData.getHeight());
      for (Map.Entry<Integer, Rectangle2D> rectEntry : rxRects.entrySet()) {
        final int rxid = rectEntry.getKey();
        Rectangle2D rxRect = rectEntry.getValue();
        Rectangle2D intersection = getIntersection(txRect, rectEntry.getValue());
        if (intersection == null) {
          continue;
        }
        VpClipData clipData = new VpClipData();
        // 输出参数
        clipData.setOutX((int) (intersection.getMinX() - rxRect.getMinX()));
        clipData.setOutY((int) (intersection.getMinY() - rxRect.getMinY()));
        clipData.setOutWidth((int) intersection.getWidth());
        clipData.setOutHeight((int) intersection.getHeight());
        // 计算裁剪参数
        clipData.setScaledWidth((int) (txRect.getWidth() * videoData.getResolution().width
            / videoData.getClippedResolutionWidth()));
        clipData.setScaledHeight((int) (txRect.getHeight() * videoData.getResolution().height
            / videoData.getClippedResolutionHeight()));
        clipData.setScaledClipX((int) (intersection.getMinX() - txRect.getMinX()
            + txRect.getWidth() / videoData.getClippedResolutionWidth()
            * videoData.getTxClipData().getLeft()));
        clipData.setScaledClipY((int) (intersection.getMinY() - txRect.getMinY()
            + txRect.getHeight() / videoData.getClippedResolutionHeight()
            * videoData.getTxClipData().getTop()));
        clipData.setScaledClipWidth((int) intersection.getWidth());
        clipData.setScaledClipHeight((int) intersection.getHeight());

        clipData.setOriginWidth(videoData.getResolution().width);
        clipData.setOriginHeight(videoData.getResolution().height);

        double scalew = videoData.getClippedResolutionWidth() / txRect.getWidth();
        double scaleh = videoData.getClippedResolutionHeight() / txRect.getHeight();
        clipData.setOriginClipX((int) (clipData.getScaledClipX() * scalew));
        clipData.setOriginClipY((int) (clipData.getScaledClipY() * scaleh));
        clipData.setOriginClipWidth((int) (clipData.getScaledClipWidth() * scalew));
        clipData.setOriginClipHeight((int) (clipData.getScaledClipHeight() * scaleh));

        clipData.setPort(videoData.getPort());
        clipData.setAlpha(videoData.getAlpha());
        clipData.setRxId(rxid);

        // 计算拼缝裁剪
        if (videoWall.getCompensationScaleThreshold() > 0) {
          if (videoWall.getCompensationScaleThreshold() <= (1 / scalew)) {
            // 左侧拼缝补偿
            if (videoWall.getLeftCompensation() > 0  // 使能左侧拼缝补偿
                && Double.compare(intersection.getMinX(), rxRect.getMinX()) == 0  // 图层在屏幕最左侧
                && intersection.getMinX() > 0        // 图层不在大屏最左侧
                && clipData.getOriginClipX() > 0) {  // 图层不在原始视频最左侧
              clipData.setLeftCompensation(Math.min(videoWall.getLeftCompensation(), clipData.getOriginClipX()));
            }
            // 右侧拼缝补偿
            if (videoWall.getRightCompensation() > 0  // 使能右侧拼缝补偿
                && Double.compare(intersection.getMaxX(), rxRect.getMaxX()) == 0  // 图层在屏幕最右侧
                && intersection.getMaxX() < videoWall.getTotalWidth()  // 图层不在大屏最右侧
                && clipData.getOriginClipX() + clipData.getOriginClipWidth() < clipData.getOriginWidth()) {  // 图层不在原始视频最右侧
              clipData.setRightCompensation(Math.min(videoWall.getRightCompensation(),
                  clipData.getOriginWidth() - clipData.getOriginClipX() - clipData.getOriginClipWidth()));
            }
          }
          if (videoWall.getCompensationScaleThreshold() <= (1 / scaleh)) {
            // 上侧拼缝补偿
            if (videoWall.getTopCompensation() > 0  // 使能上侧拼缝补偿
                && Double.compare(intersection.getMinY(), rxRect.getMinY()) == 0  // 图层在屏幕最上侧
                && intersection.getMinY() > 0        // 图层不在大屏最上侧
                && clipData.getOriginClipY() > 0) {  // 图层不在原始视频最上侧
              clipData.setTopCompensation(Math.min(videoWall.getTopCompensation(), clipData.getOriginClipY()));
            }
            // 下侧拼缝补偿
            if (videoWall.getBottomCompensation() > 0  // 使能下侧拼缝补偿
                && Double.compare(intersection.getMaxY(), rxRect.getMaxY()) == 0  // 图层在屏幕最下侧
                && intersection.getMaxY() < videoWall.getTotalHeight()  // 图层不在大屏最下侧
                && clipData.getOriginClipY() + clipData.getOriginClipHeight() < clipData.getOriginHeight()) {  // 图层不在原始视频最下侧
              clipData.setBottomCompensation(Math.min(videoWall.getBottomCompensation(),
                  clipData.getOriginHeight() - clipData.getOriginClipY() - clipData.getOriginClipHeight()));
            }
          }
        }

        VpScreenLayers layers = rxLayers.get(rxid);
        layers.layers.add(clipData);
        rxLayers.put(rxid, layers);
      }
    }
    return rxLayers.values();
  }

  /**
   * 筛选图层.
   *
   * @param matrix  矩阵
   * @param layers  图层信息
   * @param vpconid vpconid
   * @return 筛选后的图层信息.
   */
  static Collection<VpScreenLayers> filterVp6Layers(
      VpMatrix matrix, Collection<VpScreenLayers> layers, int vpconid) {
    List<VpScreenLayers> result = new ArrayList<>();
    int maxLayerPerScreen = Vp6Constants.LAYER_PER_PORT;
    int maxTotalLayer = Vp6Constants.MAX_TOTAL_LAYER;
    int totalLayers = 0;
    for (VpScreenLayers screenLayer : layers) {
      if (totalLayers >= maxTotalLayer) {
        screenLayer.layers.clear();
        continue;
      }

      if (screenLayer.layers.size() > maxLayerPerScreen) {
        screenLayer.layers.subList(maxLayerPerScreen, screenLayer.layers.size()).clear();
      }
      totalLayers += screenLayer.layers.size();
      if (totalLayers > maxTotalLayer) {
        int subcnt = totalLayers - maxTotalLayer;
        screenLayer
            .layers
            .subList(screenLayer.layers.size() - subcnt, screenLayer.layers.size())
            .clear();
        totalLayers -= subcnt;
      }
      result.add(screenLayer);
    }
    return result;
  }

  static Collection<VpScreenLayers> updateClipIndex(Collection<VpScreenLayers> layers) {
    for (VpScreenLayers item : layers) {
      int size = item.layers.size();
      for (int i = 0; i < size; i++) {
        item.layers.get(i).setClipIndex(i);
      }
    }
    return layers;
  }

  /**
   * 计算配置.
   *
   * @param matrix  矩阵
   * @param screens 各个屏幕的图层信息.
   * @return 配置数据.
   */
  static Vp6ConfigData calculateVp6Config(
      VpMatrix matrix, VpVideoWall videoWall, Collection<VpScreenLayers> screens) {
    Vp6ConfigData data = new Vp6ConfigData();
    // init input
    for (int i = 0; i < Vp6ConfigData.IN_PORT_COUNT; i++) {
      data.inputScaler[i].inputWidth.set(Vp6ConfigData.INPUT_INIT_VALUE);
      data.inputScaler[i].inputHeight.set(Vp6ConfigData.INPUT_INIT_VALUE);
      data.inputScaler[i].outputWidth.set(Vp6ConfigData.INPUT_INIT_VALUE);
      data.inputScaler[i].outputHeight.set(Vp6ConfigData.INPUT_INIT_VALUE);
    }

    // OSD
    VpOsdData osdData = videoWall.getOsdData();
    data.osdConfig.alpha.set(osdData.getOsdAlpha());
    data.osdConfig.osdLeft.set(osdData.getOsdLeft());
    data.osdConfig.osdTop.set(osdData.getOsdTop());
    data.osdConfig.osdSegWidth.set(osdData.getOsdWidth());
    data.osdConfig.osdSegHeight.set(osdData.getOsdHeight());
    data.osdConfig.osdColor.set(ColorUtility.toRgb(osdData.getOsdColor()));
    data.osdConfig.bgColor.set(
        (ColorUtility.toRgb(osdData.getBgColor()) & 0x00ffffff)
            + (osdData.isShowLogo() ? 0x01000000 : 0));
    for (VpScreenLayers screen : screens) {
      int index = getVpconIndex(screen.rxId);
      if (index >= 0 && index < data.osdConfig.oid.length) {
        data.osdConfig.oid[index].set(VpConConfigData.generateOid(screen.oid));
      }
    }
    // 收集所有图层
    List<VpClipData> allClipDatas = new ArrayList<>();
    for (VpScreenLayers screen : screens) {
      allClipDatas.addAll(screen.layers);
    }
    int vertCutIndex = 0;
    int horzCutIndex = 0;
    // 根据端口分组，使用TreeMap，可以预测顺序
    TreeMap<Integer, List<VpClipData>> portGrouping =
        allClipDatas.stream()
            .collect(Collectors.groupingBy(VpClipData::getPort, TreeMap::new, Collectors.toList()));
    // 检查是否有视频输入
    Map.Entry<Integer, List<VpClipData>> ceilingEntry = portGrouping.ceilingEntry(0);
    if (ceilingEntry != null && ceilingEntry.getKey() < Vp6Constants.BGIMG_PORT) {
      data.osdConfig.bgColor.set(data.osdConfig.bgColor.get() | 0x80000000);
    }
    //
    for (Map.Entry<Integer, List<VpClipData>> portEntry : portGrouping.entrySet()) {
      int port = portEntry.getKey();
      if (port < 0) {
        log.error("Error port {} while calculating config.", port);
        continue;
      }
      if (portEntry.getValue().size() == 0) {
        log.error("Not clip data while calculating config.");
        continue;
      }
      VpClipData clipData = portEntry.getValue().get(0);
      // 缩小模块
      Vp6ScalerData scalerData;
      if (port < Vp6Constants.BGIMG_PORT) {
        scalerData = data.inputScaler[port];
      } else {
        scalerData = new Vp6ScalerData(); // 虚拟缩小模块，便于后面计算
      }
      scalerData.inputWidth.set(clipData.getOriginWidth());
      scalerData.inputHeight.set(clipData.getOriginHeight());
      int outWidth = Math.min(clipData.getOriginWidth(), clipData.getScaledWidth());
      int outHeight = Math.min(clipData.getOriginHeight(), clipData.getScaledHeight());
      scalerData.outputWidth.set(outWidth);
      scalerData.outputHeight.set(outHeight);

      // 根据纵向初始坐标分组，做纵向切割，使用TreeMap可以预测顺序
      Map<Integer, List<VpClipData>> clipyGrouping =
          portEntry.getValue().stream()
              .collect(
                  Collectors.groupingBy(
                      VpClipData::getScaledClipY, TreeMap::new, Collectors.toList()));
      for (Map.Entry<Integer, List<VpClipData>> clipyEntry : clipyGrouping.entrySet()) {

        int horzCnt = clipyEntry.getValue().size() - 1;
        int index = 0;
        for (VpClipData vpClipData : clipyEntry.getValue()) {
          Rectangle2D inputSize =
              new Rectangle2D(0, 0, vpClipData.getScaledWidth(), vpClipData.getScaledHeight());
          Rectangle2D outputSize =
              new Rectangle2D(0, 0, scalerData.outputWidth.get(), scalerData.outputHeight.get());
          Rectangle2D inputClip =
              new Rectangle2D(
                  vpClipData.getScaledClipX(),
                  vpClipData.getScaledClipY(),
                  vpClipData.getScaledClipWidth(),
                  vpClipData.getScaledClipHeight());
          Rectangle2D outputClip = scaleClip(inputSize, inputClip, outputSize);

          if (index % Vp6ConfigData.MAX_HORZ_CUT_CNT == 0) {
            data.vertCutData[vertCutIndex].startLine.set(
                (int) outputClip.getMinY() - vpClipData.getTopCompensation());
            data.vertCutData[vertCutIndex].videoSrc.set(port);
            vertCutIndex++;
          }
          final int screenIndex = getVpconIndex(vpClipData.getRxId());
          final int layerIndex =
              screenIndex * Vp6ConfigData.LAYER_PER_PORT + vpClipData.getClipIndex();
          // 横向切割
          data.horzCutData[horzCutIndex].horzCutCount.set(horzCnt);
          data.horzCutData[horzCutIndex].vertCutIndex.set(vertCutIndex - 1);
          data.horzCutData[horzCutIndex].startPx.set(
              (int) outputClip.getMinX() - vpClipData.getLeftCompensation());
          data.horzCutData[horzCutIndex].endPx.set(
              (int) (outputClip.getMinX() + outputClip.getWidth() - 1)
                  + vpClipData.getRightCompensation()); // end px id inclusive
          // 放大
          data.outputConfig[horzCutIndex].outputPort.set((short) screenIndex);
          data.outputConfig[horzCutIndex].outputLayer.set((short) vpClipData.getClipIndex());
          data.outputConfig[horzCutIndex].layerConfig.inputWidth.set(
              (int) outputClip.getWidth()
                  + vpClipData.getLeftCompensation()
                  + vpClipData.getRightCompensation());
          data.outputConfig[horzCutIndex].layerConfig.inputHeight.set(
              (int) outputClip.getHeight()
                  + vpClipData.getTopCompensation()
                  + vpClipData.getBottomCompensation());
          data.outputConfig[horzCutIndex].layerConfig.outputWidth.set(vpClipData.getOutWidth());
          data.outputConfig[horzCutIndex].layerConfig.outputHeight.set(vpClipData.getOutHeight());
          // 图层配置
          data.layerConfig[layerIndex].width.set(vpClipData.getOutWidth());
          data.layerConfig[layerIndex].height.set(vpClipData.getOutHeight());
          data.layerConfig[layerIndex].startPx.set(vpClipData.getOutX());
          data.layerConfig[layerIndex].startLine.set(vpClipData.getOutY());
          data.layerConfig[layerIndex].alpha.set(vpClipData.getAlpha());

          horzCutIndex++;
          index++;
        }
      }
    }

    // 关闭不需要的横向切割与输出
    while (horzCutIndex < Vp6ConfigData.PROCESS_UNIT_COUNT) {
      data.horzCutData[horzCutIndex].vertCutIndex.set(Vp6ConfigData.PROCESS_UNIT_COUNT);
      data.outputConfig[horzCutIndex].outputLayer.set((short) Vp6ConfigData.LAYER_PER_PORT);
      horzCutIndex++;
    }

    return data;
  }

  /**
   * 获取两个矩形的重叠的部分.
   *
   * @param first  第一个矩形.
   * @param second 第二个矩形.
   * @return 重叠的矩形.
   */
  static Rectangle2D getIntersection(Rectangle2D first, Rectangle2D second) {
    if (!first.intersects(second)) {
      return null;
    }
    double minX = Math.max(first.getMinX(), second.getMinX());
    double minY = Math.max(first.getMinY(), second.getMinY());
    double maxX = Math.min(first.getMaxX(), second.getMaxX());
    double maxY = Math.min(first.getMaxY(), second.getMaxY());
    return new Rectangle2D(minX, minY, maxX - minX, maxY - minY);
  }

  /**
   * 缩放clip.
   *
   * @param inputSize  输入矩阵大小
   * @param inputClip  要缩放的clip
   * @param outputSize 输出矩阵大小
   * @return 缩放后的clip
   */
  static Rectangle2D scaleClip(
      Rectangle2D inputSize, Rectangle2D inputClip, Rectangle2D outputSize) {
    BigDecimal inputWidth = BigDecimal.valueOf(inputSize.getWidth());
    BigDecimal inputHeight = BigDecimal.valueOf(inputSize.getHeight());
    BigDecimal outputWidth = BigDecimal.valueOf(outputSize.getWidth());
    BigDecimal outputHeight = BigDecimal.valueOf(outputSize.getHeight());

    BigDecimal xscaleFactor = outputWidth.divide(inputWidth, 100, RoundingMode.HALF_UP);
    BigDecimal yscaleFactor = outputHeight.divide(inputHeight, 100, RoundingMode.HALF_UP);

    BigDecimal clipX = xscaleFactor.multiply(BigDecimal.valueOf(inputClip.getMinX()));
    BigDecimal roundedClipX = clipX.setScale(0, RoundingMode.HALF_UP);

    BigDecimal clipY = yscaleFactor.multiply(BigDecimal.valueOf(inputClip.getMinY()));
    BigDecimal roundedClipY = clipY.setScale(0, RoundingMode.HALF_UP);

    BigDecimal clipWidth = xscaleFactor.multiply(BigDecimal.valueOf(inputClip.getWidth()));
    BigDecimal roundedClipWidth =
        clipWidth.add(clipX).subtract(roundedClipX).setScale(0, RoundingMode.HALF_UP);

    BigDecimal clipHeight = yscaleFactor.multiply(BigDecimal.valueOf(inputClip.getHeight()));
    BigDecimal roundedClipHeight =
        clipHeight.add(clipY).subtract(roundedClipY).setScale(0, RoundingMode.HALF_UP);

    return new Rectangle2D(
        roundedClipX.intValue(),
        roundedClipY.intValue(),
        roundedClipWidth.intValue(),
        roundedClipHeight.intValue());
  }

  static int getVpconId(int rxId) {
    return rxId & 0xfffffff8;
  }

  static int getVpconIndex(int rxId) {
    return rxId & 0x07;
  }

  static boolean isVpconPortOnline(VpMatrix matrix, int vpconid, int portIndex) {
    return matrix.isExtOnline(vpconid + portIndex);
  }

  /**
   * 获取连接信息.
   *
   * @return 连接信息.
   */
  public static Map<ConsoleData, CpuData> getConnections(VpMatrix matrix, Vp6WallCurrCfg currCfg) {
    Map<ConsoleData, CpuData> result = new HashMap<>();
    List<Integer> vpcons = currCfg.getUsedVpCons();
    for (int vpconId : vpcons) {
      VpConsoleData vpConsoleData = matrix.findVpCon(vpconId);
      if (vpConsoleData == null) {
        log.warn("Fail to find vpcon {}", vpconId);
        continue;
      }
      for (VpVideoPortInfo portInfo : currCfg.getVpconPortInfos(vpconId)) {
        ExtenderData extenderData = matrix.findExt(portInfo.txId);
        if (extenderData == null || extenderData.getCpuData() == null) {
          log.warn("Fail to find tx: {}！", portInfo.txId);
        }
        int portIndex = portInfo.portIndex;
        if (portIndex < 0 || portIndex >= vpConsoleData.getInPortCount()) {
          log.warn("Port index is error : {}!", portIndex);
          continue;
        }

        // 双HDMI的只需要连第一个
        if (extenderData.getExtenderStatusInfo().getInterfaceCount() > 1
            && portIndex >= Vp6ConfigData.IN_PORT_4K_COUNT) {
          portIndex = portIndex - Vp6ConfigData.IN_PORT_4K_COUNT;
        }

        if (vpConsoleData.getInPort(portIndex) == null
            || !vpConsoleData.getInPort(portIndex).isOnline()) {
          log.warn("Vp con {}'s port {} is not online!", vpconId, portIndex);
          continue;
        }

        result.put(vpConsoleData.getInPort(portIndex), extenderData.getCpuData());
      }
      // 收集需要断开连接的项
      for (ConsoleData consoleData : vpConsoleData.getInPortList()) {
        if (consoleData == null || result.containsKey(consoleData)) {
          continue;
        }
        result.put(consoleData, null);
      }
    }
    return result;
  }


  @Data
  static class VideoRect {

    private int left;
    private int top;
    private int width;
    private int height;
    private int txId;
    private int sourceIndex;

    VideoRect(int txId, int sourceIndex, int left, int top, int width, int height) {
      this.txId = txId;
      this.sourceIndex = sourceIndex;
      this.left = left;
      this.top = top;
      this.width = width;
      this.height = height;
    }
  }
}
