package com.mc.tool.caesar.vpm.util.vp;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Delegate;

/**
 * .
 */
public class VpVideoDataWrapper implements VpVideoData {

  @Getter
  private final VpResolution resolution;

  @Getter
  private final VpTxClipData txClipData;

  @Getter
  @Delegate(types = VpVideoData.class)
  private final VpVideoData videoData;

  /**
   * 窗口的索引.
   */
  @Getter
  private final int index;

  /**
   * 是否为4k分割的窗口.
   */
  @Getter
  private final boolean is4kSplit;

  @Getter
  @Setter
  private int port = -1;

  @Getter
  @Setter
  private int channel = -1;

  public VpVideoDataWrapper(VpVideoData videoData, VpResolution resolution, int index) {
    this(videoData, resolution, index, false);
  }

  /**
   * .
   */
  public VpVideoDataWrapper(
      VpVideoData videoData, VpResolution resolution, int index, boolean is4kSplit) {
    this(videoData, resolution, new VpTxClipData(0, 0, 0, 0), index, is4kSplit);
  }

  /**
   * .
   *
   * @param videoData 视频窗口数据
   * @param resolution 信号源分辨率
   * @param txClipData 信号源裁剪信息
   * @param index      窗口索引
   * @param is4kSplit  是否为4k切割的窗口
   */
  public VpVideoDataWrapper(
      VpVideoData videoData, VpResolution resolution, VpTxClipData txClipData, int index,
      boolean is4kSplit) {
    this.videoData = videoData;
    this.resolution = resolution;
    this.txClipData = txClipData;
    this.index = index;
    this.is4kSplit = is4kSplit;
  }

  public int getClippedResolutionWidth() {
    return Math.max(resolution.width - txClipData.getLeft() - txClipData.getRight(), 0);
  }

  public int getClippedResolutionHeight() {
    return Math.max(resolution.height - txClipData.getTop() - txClipData.getBottom(), 0);
  }

  public boolean isValid() {
    return getClippedResolutionWidth() > 0 && getClippedResolutionHeight() > 0;
  }
}
