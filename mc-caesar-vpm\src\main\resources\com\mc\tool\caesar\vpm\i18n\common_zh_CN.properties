#Generated by ResourceBundle Editor (http://essiembre.github.io/eclipse-rbe/)
systemedit.switching.ip=PDU IP
CaesarOfficePage.title=\u573A\u666F
alert.can_not_save_scenario=\u73B0\u5728\u4E0D\u80FD\u4FDD\u5B58\u9884\u6848\uFF01
alert.device_busy=\u8BBE\u5907\u6B63\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002
alert.reboot_alert.text=\u8BF7\u91CD\u65B0\u8FDE\u63A5\u4E3B\u673A\u8BBE\u5907\uFF0CIP\u5730\u5740\u5DF2\u66F4\u6539\u4E3A\uFF1A
alert.reboot_alert.title=\u91CD\u542F
alert.task.message=\u6B63\u5728\u91CD\u65B0\u8FDE\u63A5...
caesar_menu.configuration=\u914D\u7F6E
caesar_menu.download=\u4E0B\u8F7D\u914D\u7F6E
caesar_menu.refresh=\u5237\u65B0
caesar_menu.save=\u4FDD\u5B58\u5728\u7EBF\u914D\u7F6E
caesar_menu.upload=\u4E0A\u4F20\u914D\u7F6E
con_hotkey.apply=\u5E94\u7528
con_hotkey.cancel=\u53D6\u6D88
con_hotkey.con_hotkey_management=\u8F6E\u8BE2\u70ED\u952E\u8BBE\u7F6E
con_hotkey.con_list=\u7BA1\u63A7\u7AEF\u5217\u8868
con_hotkey.copy_btn=\u590D\u5236\u5F53\u524D\u8F6E\u8BE2\u70ED\u952E\u5217\u8868\u5230\u5176\u4ED6\u7BA1\u63A7\u7AEF
con_hotkey.table.id=ID
con_hotkey.table.name=\u540D\u79F0
con_right.con_cpu_right_management=\u7BA1\u63A7\u7AEF\u6743\u9650\u7BA1\u7406
con_right.con_list=\u7BA1\u63A7\u7AEF\u5217\u8868
connect_device.connecting=\u6B63\u5728\u8FDE\u63A5{0}
connect_device.error.message=\u65E0\u6CD5\u8FDE\u63A5\u5230{0}\uFF01
connect_device.error.title=\u8FDE\u63A5\u5931\u8D25
connect_device.load_error=\u52A0\u8F7D\u5931\u8D25\uFF01
connect_device.loading=\u6B63\u5728\u52A0\u8F7D{0}...
connect_device.wrong_username_password=\u5BC6\u7801\u8D26\u53F7\u9519\u8BEF\u6216\u8005\u6CA1\u6709\u767B\u5F55\u6743\u9650!
copy_cpu_right_dialog.con_cpu_right.source_header.title=\u53EF\u7528\u7BA1\u63A7\u7AEF\u5217\u8868
copy_cpu_right_dialog.con_cpu_right.target_header.title=\u590D\u5236\u914D\u7F6E\u7BA1\u63A7\u7AEF\u5217\u8868
copy_cpu_right_dialog.con_cpu_right.title=\u590D\u5236\u6743\u9650\u914D\u7F6E\u5230\u5176\u4ED6\u7BA1\u63A7\u7AEF
copy_cpu_right_dialog.user_cpu_right.source_header.title=\u53EF\u7528\u7528\u6237\u7EC4\u5217\u8868
copy_cpu_right_dialog.user_cpu_right.target_header.title=\u590D\u5236\u914D\u7F6E\u7528\u6237\u5217\u8868
copy_cpu_right_dialog.user_cpu_right.title=\u590D\u5236\u6743\u9650\u914D\u7F6E\u5230\u5176\u4ED6\u7528\u6237
copy_hotkey_dialog.con_hotkey.source_header.title=\u53EF\u7528\u7BA1\u63A7\u7AEF\u5217\u8868
copy_hotkey_dialog.con_hotkey.target_header.title=\u590D\u5236\u914D\u7F6E\u7BA1\u63A7\u7AEF\u5217\u8868
copy_hotkey_dialog.con_hotkey.title=\u590D\u5236\u5F53\u524D\u8F6E\u8BE2\u70ED\u952E\u5217\u8868\u5230\u5176\u4ED6\u7BA1\u63A7\u7AEF
copy_macro_dialog.con_source_header=\u53EF\u7528\u7BA1\u63A7\u7AEF\u5217\u8868
copy_macro_dialog.con_target_header=\u8981\u590D\u5236\u914D\u7F6E\u7684\u7BA1\u63A7\u7AEF\u5217\u8868
copy_macro_dialog.title=\u590D\u5236\u5B8F\u914D\u7F6E
copy_macro_dialog.user_source_header=\u53EF\u7528\u7528\u6237\u5217\u8868
copy_macro_dialog.user_target_header=\u8981\u590D\u5236\u914D\u7F6E\u7684\u7528\u6237\u5217\u8868
cpu_right.apply=\u5E94\u7528
cpu_right.cancel=\u53D6\u6D88
cpu_right.full_access=\u5B8C\u5168\u8BBF\u95EE
cpu_right.menu.full=\u5206\u914D\u64CD\u4F5C\u6A21\u5F0F\u6743\u9650
cpu_right.menu.no=\u5206\u914D\u65E0\u6743\u9650\u6A21\u5F0F
cpu_right.menu.video=\u5206\u914D\u89C6\u9891\u6743\u9650\u6A21\u5F0F
cpu_right.name=\u540D\u79F0
cpu_right.no_access=\u65E0\u6743\u9650\u6A21\u5F0F
cpu_right.tip=\u4F7F\u7528\u6309\u952EF\uFF08\u5B8C\u5168\u8BBF\u95EE\uFF09\u3001V\uFF08\u89C6\u9891\u6A21\u5F0F\uFF09\u548CN\uFF08\u65E0\u6743\u9650\u6A21\u5F0F\uFF09\u8FDB\u884C\u6743\u9650\u7BA1\u7406
cpu_right.user_cpu_right_management=\u7528\u6237\u6743\u9650\u7BA1\u7406
cpu_right.user_list=\u7528\u6237\u5217\u8868
cpu_right.video_access=\u89C6\u9891\u8BBF\u95EE
cross_screen.cancel=\u53D6\u6D88
cross_screen.column=\u5217\uFF1A
cross_screen.commit=\u63D0\u4EA4
cross_screen.commit.warn_content=\u63D0\u4EA4\u540E\u5C06\u65E0\u6CD5\u4FEE\u6539\u7BA1\u63A7\u7AEF\u7684\u4F4D\u7F6E\u4E0E\u63A7\u5236\u7BA1\u63A7\u7AEF\uFF0C\u662F\u5426\u7EE7\u7EED\u63D0\u4EA4\uFF1F
cross_screen.commit.warn_title=\u63D0\u793A
cross_screen.control=\u63A7\u5236
cross_screen.display=\u7EA2\u6846\u63D0\u793A(s)
cross_screen.mode=\u8DE8\u5C4F\u6A21\u5F0F
cross_screen.mode.auto=\u81EA\u52A8\u8DE8\u5C4F
cross_screen.mode.manual=\u624B\u52A8\u8DE8\u5C4F
cross_screen.name=\u540D\u79F0
cross_screen.row=\u884C\uFF1A
cross_screen.rx_list=\u8DE8\u5C4F\u7BA1\u63A7\u7AEF
cross_screen.rx_list.index=\u5E8F\u53F7
cross_screen.rx_list.name=\u540D\u79F0
cross_screen.setting=\u8BBE\u7F6E
cross_screen.source_list_title=\u4FE1\u53F7\u6E90\u5217\u8868
export.config=\u8BBE\u5907\u914D\u7F6E
export.graph=\u7CFB\u7EDF\u56FE
export.report=\u62A5\u544A
export.status=\u7CFB\u7EDF\u72B6\u6001
export.status_and_log=\u7CFB\u7EDF\u72B6\u6001+\u65E5\u5FD7
export.status.fail=\u4FDD\u5B58\u5931\u8D25!
export.status.ok=\u4FDD\u5B58\u6210\u529F!
export_report.empty_MessageNotify=\u9009\u62E9\u4E3A\u7A7A!
export_report.empty_file_path=\u6587\u4EF6\u8DEF\u5F84\u4E3A\u7A7A\uFF01
export_report.file_path=\u6587\u4EF6\u8DEF\u5F84\uFF1A
export_report.filechoose_title=\u4FDD\u5B58\u62A5\u544A
export_report.page1_title=\u9009\u62E9\u5185\u5BB9
export_report.save_success=\u4FDD\u5B58\u62A5\u544A\u6210\u529F\uFF01
export_report.wizard_title=\u914D\u7F6E\u62A5\u544A
hostconfiguration.activate_tab=\u6FC0\u6D3B\u914D\u7F6E
hostconfiguration.general_apply_btn=\u5E94\u7528
hostconfiguration.general_cancel_btn=\u53D6\u6D88
hostconfiguration.general_tab=\u901A\u7528\u914D\u7F6E
hostconfiguration.grid_tab=\u7EA7\u8054\u914D\u7F6E
hostconfiguration.network_apply_btn=\u5E94\u7528
hostconfiguration.network_cancel_btn=\u53D6\u6D88
hostconfiguration.network_tab=\u7F51\u7EDC\u914D\u7F6E
hostconfiguration.redundancy_apply_btn=\u5E94\u7528
hostconfiguration.redundancy_cancel_btn=\u53D6\u6D88
hostconfiguration.redundancy_tab=\u53CC\u673A\u5197\u4F59
hostconfiguration.snmp_apply_btn=\u5E94\u7528
hostconfiguration.snmp_cancel_btn=\u53D6\u6D88
hostconfiguration.snmp_tab=SNMP\u914D\u7F6E
hostconfiguration.syslog_tab=\u7CFB\u7EDF\u65E5\u5FD7
hotkey_selection_view.source_header=\u53EF\u7528\u63A5\u5165\u7AEF\u8F6E\u8BE2\u5217\u8868
hotkey_selection_view.target_header=\u8F6E\u8BE2\u70ED\u952E\u5217\u8868
macro_key.apply=\u5E94\u7528
macro_key.cancel=\u53D6\u6D88
macro_key.con_access_block_title=\u7BA1\u63A7\u7AEF\u5B8F\u5217\u8868\u914D\u7F6E
macro_key.con_source_list_title=\u7BA1\u63A7\u7AEF\u5217\u8868
macro_key.copy_macro_list=\u590D\u5236\u5B8F\u5217\u8868
macro_key.copy_to_other_rx=\u590D\u5236\u5B8F\u914D\u7F6E\u81F3\u5176\u4ED6\u7BA1\u63A7\u7AEF
macro_key.copy_to_other_user=\u590D\u5236\u5B8F\u914D\u7F6E\u81F3\u5176\u4ED6\u7528\u6237
macro_key.current_con_device=\u5F53\u524D\u7BA1\u63A7\u7AEF
macro_key.current_user=\u5F53\u524D\u7528\u6237
macro_key.delete_macro_list=\u5220\u9664\u5B8F\u5217\u8868
macro_key.delete_macro_list.delete_all=\u5220\u9664\u6240\u6709
macro_key.delete_macro_list.delete_confirm_content=\u9700\u8981\u5220\u9664\u5DF2\u9009\u62E9\u7684\u6309\u952E\u8FD8\u662F\u6240\u6709\u6309\u952E\uFF1F
macro_key.delete_macro_list.delete_confirm_title=\u5220\u9664\u5B8F
macro_key.delete_macro_list.delete_current=\u5220\u9664\u5DF2\u9009
macro_key.disconnected_cpu=\u65AD\u5F00\u63A5\u5165\u7AEF
macro_key.function=\u529F\u80FD
macro_key.index=\u5E8F\u53F7
macro_key.logout=\u9000\u51FA
macro_key.macro=\u5B8F
macro_key.name=\u540D\u79F0
macro_key.paste_macro_list=\u7C98\u8D34\u5B8F\u5217\u8868
macro_key.user_access_block_title=\u7528\u6237\u5B8F\u5217\u8868\u914D\u7F6E
macro_key.user_source_list_title=\u7528\u6237\u5217\u8868
matrixgrid.bind.alert.content.text=\u8FDE\u63A5\u7684\u7AEF\u53E35656\u5DF2\u88AB\u4F7F\u7528
matrixgrid.bind.alert.title.text=\u63D0\u793A
matrixgrid.connect=\u8FDE\u63A5
matrixgrid.matrix=\u4E3B\u673A
matrixgrid.wizard.hostconfig.delete=\u5220\u9664
matrixgrid.wizard.hostconfig.duplicate=\u91CD\u590DIP\u5730\u5740
matrixgrid.wizard.hostconfig.matrix=\u4E3B\u673A
matrixgrid.wizard.hostconfig.name_dulplicate=\u540D\u79F0\u91CD\u590D
matrixgrid.wizard.hostconfig.name_empty=\u540D\u79F0\u4E3A\u7A7A
matrixgrid.wizard.hostconfig.name_format_error=\u540D\u79F0\u6709\u8BEF
matrixgrid.wizard.hostconfig.name_too_long=\u540D\u79F0\u592A\u957F
matrixgrid.wizard.hostconfig.port_error=\u7AEF\u53E3\u6570\u9519\u8BEF
matrixgrid.wizard.hostconfig.unaccessable=\u65E0\u6CD5\u8FDE\u63A5
matrixgrid.wizard.hostconfig.uncomplete=\u6570\u636E\u4E0D\u5B8C\u5168
matrixgrid.wizard.hostconfig.valid=\u6709\u6548
matrixgrid.wizard.hostconfig.validate=\u6821\u9A8C
matrixgrid.wizard.indicator.activate=\u6FC0\u6D3B\u4E3B\u673A\u914D\u7F6E
matrixgrid.wizard.indicator.gridname=\u7EA7\u8054\u540D\u79F0
matrixgrid.wizard.indicator.gridsystem=\u7EA7\u8054\u7CFB\u7EDF
matrixgrid.wizard.indicator.host=\u4E3B\u673A\u914D\u7F6E
matrixgrid.wizard.indicator.idprocess=ID\u5904\u7406
matrixgrid.wizard.indicator.prepare=\u524D\u671F\u51C6\u5907
offline_manager.delete_btn=\u5220\u9664
offline_manager.device=\u8BBE\u5907
offline_manager.empty_alert.text=\u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u9879\uFF01
offline_manager.empty_alert.title=\u63D0\u793A
offline_manager.ext_name=\u5916\u8BBE
offline_manager.name=\u8BBE\u5907
offline_manager.title=\u79BB\u7EBF\u8BBE\u5907\u7BA1\u7406
page.con_cpu_right=\u7BA1\u63A7\u7AEF\u6743\u9650
page.con_hotkey_configuration=\u7BA1\u63A7\u7AEF\u8F6E\u8BE2\u70ED\u952E\u914D\u7F6E
page.con_macro_view=\u7BA1\u63A7\u7AEF\u5B8F\u914D\u7F6E
page.refreshing=\u6B63\u5728\u5237\u65B0...
page.switch=\u5207\u6362
page.switch_to_operation=\u662F\u5426\u5207\u6362\u5230\u7CFB\u7EDF\u64CD\u4F5C\u9875\u9762\uFF1F
page.user_cpu_right=\u7528\u6237\u6743\u9650
page.user_group_cpu_right=\u7528\u6237\u7EC4\u6743\u9650
page.user_macro_view=\u7528\u6237\u5B8F\u914D\u7F6E
report.assignment=\u5206\u914D
report.con_acl=\u8BBF\u95EE\u63A7\u5236
report.con_devices=CON\u7BA1\u63A7\u7AEF
report.con_favorites=\u6536\u85CF\u5939
report.con_macros=\u5B8F
report.cpu_devices=CPU\u63A5\u5165\u7AEF
report.ext_units=\u6269\u5C55\u5355\u5143
report.matrix_view=\u4E3B\u673A\u89C6\u56FE
report.select_all=\u9009\u62E9\u5168\u90E8
report.system=\u7CFB\u7EDF
report.user=\u7528\u6237
report.user_acl=\u8BBF\u95EE\u63A7\u5236
report.user_favorites=\u6536\u85CF\u5939
report.user_macros=\u5B8F
save_config.alert.download_complete=\u4E0B\u8F7D\u5B8C\u6210
save_config.alert.download_failed=\u4E0B\u8F7D\u5931\u8D25
save_config.alert.title=\u4FE1\u606F
save_config.alert.upload_complete=\u4E0A\u4F20\u5B8C\u6210
save_config.alert.upload_failed=\u4E0A\u4F20\u5931\u8D25
save_config.download_title=\u4E0B\u8F7D
save_config.page1.tips1=\u6B65\u9AA4
save_config.page1.tips2=1.\u8FDE\u63A5
save_config.page1.tips3=2.\u9009\u62E9\u914D\u7F6E\u6587\u4EF6
save_config.page1.tips4=2.\u9009\u62E9\u914D\u7F6E\u69FD\u4F4D
save_config.page2.address=\u4E3B\u673A\u540D/IP\u5730\u5740
save_config.page2.errorMsg.text1=\u7528\u6237\u6CA1\u6709\u7BA1\u7406\u5458\u6743\u9650,\u4E0D\u5141\u8BB8\u4E0A\u4F20\u3002
save_config.page2.errorMsg.text2=\u7528\u6237\u4E0D\u5B58\u5728
save_config.page2.errorMsg.text3=\u8FDE\u63A5\u5931\u8D25
save_config.page2.errorMsg.text4=\u4E3B\u673A\u5FD9
save_config.page2.errorMsg.text5=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
save_config.page2.password=\u5BC6\u7801
save_config.page2.user=\u7528\u6237
save_config.page2.verify=\u9A8C\u8BC1
save_config.page3.activate_alert.text=\u6FC0\u6D3B\u914D\u7F6E\u6587\u4EF6\uFF0C\u4E3B\u673A\u5FC5\u987B\u91CD\u542F\uFF0C\u4F60\u771F\u7684\u786E\u8BA4\u6FC0\u6D3B\u914D\u7F6E\u5417\uFF1F
save_config.page3.activate_alert.title=\u6FC0\u6D3B\u914D\u7F6E
save_config.page3.activate_checkbox=\u4E0A\u4F20\u540E\u6FC0\u6D3B\u914D\u7F6E(\u4E3B\u673A\u5C06\u4F1A\u91CD\u542F\uFF01)
save_config.page3.addressCol=IP\u5730\u5740
save_config.page3.download=\u4E0B\u8F7D
save_config.page3.downloadBtn=\u4E0B\u8F7D
save_config.page3.fileCol=\u6587\u4EF6
save_config.page3.infoCol=\u4FE1\u606F
save_config.page3.nameCol=\u540D\u79F0
save_config.page3.title=\u9009\u62E9\u914D\u7F6E\u6587\u4EF6
save_config.page3.uploadBtn=\u4E0A\u4F20
save_config.page3.versionCol=\u7248\u672C
save_config.upload_title=\u4E0A\u4F20
searchForDevices.title=\u67E5\u627E\u8BBE\u5907
toolbar.meeting_room=\u4F1A\u8BAE\u5BA4\u5E03\u5C40
toolbar.save_scenario=\u4FDD\u5B58\u9884\u6848
user_group.name=\u540D\u79F0
user_group_right.delete_button=\u5220\u9664\u7528\u6237\u7EC4
user_group_right.edit_button=\u7F16\u8F91\u7528\u6237\u7EC4
user_group_right.new_button=\u65B0\u5EFA\u7528\u6237\u7EC4
user_group_right.user_group_cpu_right_management=\u7528\u6237\u7EC4\u6743\u9650\u7BA1\u7406
user_group_right.user_group_list=\u7528\u6237\u7EC4\u5217\u8868
videowall.check.vp6_screen_layer_limit=\u4E00\u4E2A\u5C4F\u5E55\u5185\u7684\u56FE\u5C42\u6570\u5FC5\u987B\u4E0D\u8D85\u8FC7{0}!
videowall.check.vp6_layer_limit=\u4E00\u4E2AVP6\u5185\u7684\u56FE\u5C42\u6570\u5FC5\u987B\u4E0D\u8D85\u8FC7{0}!
videowall.check.vp6_window_limit=2K\u7A97\u53E3\u6570\u6700\u591A{0}\u4E2A\uFF0C4K/DHDMI\u7A97\u53E3\u6570\u6700\u591A{1}\u4E2A!
videowall.check.vp7_screen_layer_limit={1}\u63A5\u53E3\u6A21\u5F0F\u4E0B\uFF0C\u4E00\u4E2A\u5C4F\u5E55\u5185\u7684\u56FE\u5C42\u6570\u5FC5\u987B\u4E0D\u8D85\u8FC7{0}!
videowall.check.vp7_window_limit=2K/DHDMI/4K30\u4FE1\u53F7\u6700\u591A{0}\u4E2A\uFF0C4K60\u4FE1\u53F7\u6700\u591A{1}\u4E2A!
videowall.check.vp7_invalid_screen=\u5F53\u524D\u63A5\u53E3\u6A21\u5F0F\u4E3A{0},\u6B64\u5C4F\u5E55\u65E0\u6548!
videowall.mode=\u6A21\u5F0F\u8C03\u7528\u65B9\u5F0F
videowall.mode.auto=\u81EA\u52A8\u53D1\u5E03
videowall.mode.manual=\u624B\u52A8\u53D1\u5E03
videowall.output=\u8F93\u51FA
videowall.output.clock=\u65F6\u949F
videowall.output.enable=\u4F7F\u80FD
videowall.output.horz_active_video_time=\u6C34\u5E73\u5206\u8FA8\u7387
videowall.output.horz_back_porch=\u6C34\u5E73\u540E\u6CBF
videowall.output.horz_front_porch=\u6C34\u5E73\u524D\u6CBF
videowall.output.horz_polarity=\u6C34\u5E73\u6781\u6027
videowall.output.horz_sync=\u6C34\u5E73\u540C\u6B65
videowall.output.vert_active_video_time=\u5782\u76F4\u5206\u8FA8\u7387
videowall.output.vert_back_porch=\u5782\u76F4\u540E\u6CBF
videowall.output.vert_front_porch=\u5782\u76F4\u524D\u6CBF
videowall.output.vert_polarity=\u5782\u76F4\u6781\u6027
videowall.output.vert_sync=\u5782\u76F4\u540C\u6B65
videowall.scenario.limit_error=\u6700\u591A\u53EA\u80FD\u4FDD\u5B58{0}\u4E2A\u9884\u6848\uFF01
wizard_pane.next_btn=\u4E0B\u4E00\u6B65
wizard_pane.previous_btn=\u4E0A\u4E00\u6B65
export.status.saving=\u6B63\u5728\u4FDD\u5B58\u72B6\u6001
systemedit.master=\u4E3B
systemedit.slave=\u4ECE
systemedit.gridline=\u7EA7\u8054\u7EBF
matrixgrid.wizard.hostconfig.port_too_much=\u7AEF\u53E3\u603B\u6570\u8FC7\u591A
multiscreen.row=\u884C
multiscreen.column=\u5217
multiscreen.layout=RX\u5E03\u5C40
toolbar.save_as_scenario=\u53E6\u5B58\u4E3A\u9884\u6848
multiscreen.connection.full=\u5B8C\u5168\u8FDE\u63A5
multiscreen.connection.video=\u89C6\u9891\u8FDE\u63A5
multiscreen.rx_list=\u7EC4\u5408\u7BA1\u63A7\u7AEF
multiscreen.video_conn=\u89C6\u9891\u8FDE\u63A5
multiscreen.full_conn=\u5B8C\u5168\u8FDE\u63A5
systemedit.list.index=\u5E8F\u53F7
systemedit.list.id=ID
systemedit.list.device=\u8BBE\u5907 
systemedit.list.port=\u7AEF\u53E3
systemedit.list.type=\u7C7B\u578B
systemedit.list.serial=\u5E8F\u5217\u53F7
systemedit.exporting=\u6B63\u5728\u5BFC\u51FA\u5916\u8BBE\u4FE1\u606F
systemedit.exportsuccess=\u5BFC\u51FA\u6210\u529F
systemedit.exporterror=\u5BFC\u51FA\u5931\u8D25
systemedit.importing=\u6B63\u5728\u5BFC\u5165\u5916\u8BBE\u4FE1\u606F
systemedit.importsuccess=\u5BFC\u5165\u6210\u529F
systemedit.importerror=\u5BFC\u5165\u5931\u8D25
systemedit.importerror.formaterror=\u90E8\u5206\u884C\u7684\u683C\u5F0F\u6709\u8BEF
systemedit.importerror.typeerror=\u90E8\u5206\u9879\u4E0D\u5B58\u5728\u6216\u8005\u7C7B\u578B\u4E0D\u5339\u914D
systemedit.importerror.nameerror=\u90E8\u5206\u9879\u7684\u540D\u79F0\u957F\u5EA6\u4E0D\u7B26\u5408\u89C4\u8303
systemedit.importerror.iderror=\u90E8\u5206\u9879\u7684ID\u8303\u56F4\u9519\u8BEF\u6216\u8005\u91CD\u590D
systemedit.importerror.unknownerror=\u672A\u77E5\u9519\u8BEF
systemedit.title.success=\u6210\u529F
systemedit.title.fail=\u5931\u8D25
systemedit.switch=\u5207\u6362\u5230\u4E3B\u673A
systemedit.topological=\u7EA7\u8054\u62D3\u6251\u56FE
videowall.check.vpcon_4k_window_limit=4K\u7A97\u53E3\u6570\u5FC5\u987B\u4E0D\u8D85\u8FC7{0}!
videowall.check.vpcon_2k_window_limit=2K\u7A97\u53E3\u6570\u5FC5\u987B\u4E0D\u8D85\u8FC7{0}!
videowall.not_created=\u672A\u521B\u5EFA
videowall.bgimg.uploadpath=\u80CC\u666F\u56FE\u8DEF\u5F84\uFF1A
videowall.bgimg.selectbtn=\u9009\u62E9
videowall.bgimg.upload=\u4E0A\u4F20
videowall.bgimg.cancel=\u53D6\u6D88
videowall.bgimg.title=\u4E0A\u4F20\u80CC\u666F\u56FE
cross_screen.fail_to_create=\u7531\u4E8E\u6CA1\u6709\u8DB3\u591F\u7684\u7A7A\u95F4\uFF0C\u65E0\u6CD5\u521B\u5EFA\u8DE8\u5C4F\u7EC4\uFF01
connect_device.error_version=\u4E0D\u652F\u6301\u4E3B\u673A\u7248\u672C{0}\uFF0C\u8BF7\u5347\u7EA7\u5230\u65B0\u7248\u672C\u7684VPM\u8F6F\u4EF6\u3002
check.try=\u6B63\u5728\u521D\u59CB\u5316\u6570\u636E\uFF0C\u8BF7\u7A0D\u540E\u5237\u65B0\u3002
user.videowallright.selected=\u5DF2\u6388\u6743\u89C6\u9891\u5899
user.videowallright.unselected=\u53EF\u9009\u89C6\u9891\u5899
cross_screen.mode.irregular=\u5F02\u5F62\u8DE8\u5C4F
systemedit.switching.index=\u5E8F\u53F7
systemedit.switching.id=ID
systemedit.switching.device=\u8BBE\u5907
systemedit.switching.port=\u7AEF\u53E3
systemedit.switching.mode=\u5F00\u5173\u673A\u6A21\u5F0F
systemedit.switching.outlet=\u7535\u6E90\u63D2\u5EA7
systemedit.switching.operation=\u64CD\u4F5C
systemedit.switching.poweron=\u5F00\u673A
systemedit.switching.poweroff=\u5173\u673A
systemedit.switching.reboot=\u91CD\u542F
systemedit.switching.poweron.alert=\u662F\u5426\u5BF9\u5173\u673APC\u8FDB\u884C\u8FDC\u7A0B\u5F00\u673A\uFF1F
systemedit.switching.poweroff.alert=\u662F\u5426\u5F3A\u5236\u5173\u673A\uFF1F
systemedit.switching.reboot.alert=\u662F\u5426\u5F3A\u5236\u91CD\u542F\uFF1F
systemedit.switching.networkwake=\u7F51\u5361\u5524\u9192
systemedit.switching.pcpowercontrol=\u63A7\u5236PC\u7535\u6E90
systemedit.switching.disable=\u7981\u7528
systemedit.switching.editPduIpDialog.title=\u8BBE\u7F6EPDU IP
systemedit.speed.index=\u5E8F\u53F7
systemedit.speed.name=\u540D\u79F0
systemedit.speed.speed=\u901F\u5EA6
videowall.logiclayout.enable=\u4F7F\u80FD
videowall.logiclayout.row=\u903B\u8F91\u5C4F\u5E55\u884C\u6570
videowall.logiclayout.column=\u903B\u8F91\u5C4F\u5E55\u5217\u6570
systemedit.page.selector.graph=\u62D3\u6251
systemedit.page.selector.list=\u5217\u8868
systemedit.page.selector.switch=\u5F00\u5173\u673A
systemedit.page.selector.speed=\u901F\u7387\u8BBE\u7F6E
systemedit.exporting.optModule=\u6B63\u5728\u5BFC\u51FA\u5149\u6A21\u5757\u4FE1\u606F
txGroupsManager.title=\u65E0\u6548TXRX\u5206\u7EC4\u7BA1\u7406
txGroupsManager.selectAll=\u5168\u9009
page.saving=\u6B63\u5728\u4FDD\u5B58...
videowall.check.vpcon_offline=VP6\u65E0\u6CD5\u901A\u8BAF!
hostconfiguration.event_tab=\u4E8B\u4EF6\u914D\u7F6E
hostconfiguration.event_apply_btn=\u5E94\u7528
hostconfiguration.event_cancel_btn=\u53D6\u6D88
videowall.check.screen_duplicate=\u5927\u5C4F\u5B58\u5728\u91CD\u590D\u5C4F\u5E55
multiscreen.connection.disconnect=\u65AD\u5F00\u8FDE\u63A5
cross_screen.mode.linkage=\u56DB\u753B\u9762\u8054\u52A8\u6A21\u5F0F
enable=\u4F7F\u80FD
systemedit.page.selector.offline=\u79BB\u7EBF\u8BBE\u5907
name=\u540D\u79F0
offline_manager.delete_selected=\u5220\u9664\u9009\u4E2D\u9879
offline_manager.delete_checked=\u5220\u9664\u52FE\u9009\u9879
offline_manager.delete_checked.alert=\u786E\u5B9A\u5220\u9664\u52FE\u9009\u7684\u9879\u5417\uFF1F
check.matrix_version=\u4E3B\u63A7FPGA\u7248\u672C\u4E0D\u5339\u914D\uFF0C\u8BF7\u5347\u7EA7\u3002
videowall.audio_group.dialog.title=\u8BF7\u9009\u62E9\u97F3\u9891\u7EC4
menu_disconnect=\u65AD\u5F00
resWidth-label=\u5BBD
resHeight-label=\u9AD8
videowall.resync=\u91CD\u65B0\u540C\u6B65\u89C6\u9891
videowall.resync.hint=\u70B9\u51FB\u53F3\u4FA7\u6309\u94AE\u540C\u6B65
videowall.resync.confirm=\u786E\u8BA4\u662F\u5426\u91CD\u65B0\u540C\u6B65\uFF1F
connect_device.error_matrix_min_vpm=\u5F53\u524DVPM\u7248\u672C\u8FC7\u4F4E\uFF0C\u4E3B\u673A\u8981\u6C42\u6700\u4F4EVPM\u7248\u672C\u4E3A{0}

