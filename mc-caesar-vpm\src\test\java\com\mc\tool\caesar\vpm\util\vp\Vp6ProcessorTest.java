package com.mc.tool.caesar.vpm.util.vp;

import com.google.common.io.CharStreams;
import com.google.gson.Gson;
import com.mc.common.lang.reflect.ParameterizedTypeImpl;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpMatrix;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpVideoWall;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator.StatusConfig;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * .
 */
public class Vp6ProcessorTest {

  @Before
  public void before() {
    System.setProperty("generator-mode", "true");
    System.setProperty("no-fx-mode", "true");
  }

  @Test
  public void test4vp6Config() {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/full_4vp6.json";
    VideoWallGenerator videoWallGenerator =
        VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, VideoWallGenerator.VideoWallType.VP6);

    CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
    CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
    Vp6WallCurrCfg vp6WallCurrCfg = new Vp6WallCurrCfg();
    Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);

    Assert.assertEquals(4, vp6WallCurrCfg.getUsedVpCons().size());

    int index = 1;
    for (Integer key : new TreeSet<>(vp6WallCurrCfg.getUsedVpCons())) {
      Vp6ConfigData configData = vp6WallCurrCfg.getVpconConfigData(key);
      System.out.println(configData.toString());
      String resultPath =
          "com/mc/tool/caesar/vpm/videowall/unit/common/full_4vp6_" + index + ".txt";
      checkResult(classLoader, configData, resultPath);
      index++;
    }
  }

  @Test
  public void testCommonConfig() {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    String[] configs =
        new String[] {
            "clip_2k_with_compensation",
            "2k_with_compensation_disable_top_full",
            "2k_with_compensation_disable_left_full",
            "2k_with_compensation_scale_not_enough_full",
            "2k_with_compensation_disable_full",
            "2k_with_compensation_oversize",
            "2k_with_compensation_center",
            "2k_with_compensation_3x3_full",
            "2k_with_compensation_full",
            "clip_4k_error_width",
            "clip_2k_error_height",
            "clip_2k_error_width",
            "clip_4k_small_right",
            "clip_4k_small_left",
            "clip_4k_bottom_left",
            "clip_4k_bottom_right",
            "clip_4k_top_right",
            "clip_4k_top_left",
            "clip_2k_bottom_left",
            "clip_2k_bottom_right",
            "clip_2k_top_right",
            "clip_2k_top_left",
            "4k_full_with_positive_margin",
            "4k_full_with_negative_margin",
            "4k_more_window_with_bgimg",
            "2_vp6_cross",
            "2k_global_more_layer",
            "dhdmi_right",
            "dhdmi_left",
            "dhdmi_dual_revert",
            "dhdmi_dual",
            "smallscreen_with_bgimg",
            "full_with_bgimg",
            "4k_2k_more_layer",
            "4k_2k_more_window",
            "4k_4_window_right_more_layer",
            "4k_4_window_left_more_layer",
            "4k_4_window_no_more_layer",
            "4k_more_layer",
            "4k_more_layer_with_bgimg",
            "4k_more_window",
            "2k_more_layer",
            "2k_more_layer_with_bgimg",
            "2k_more_window",
            "4k_2k_samescreen",
            "2k_4k_samescreen",
            "4k_full",
            "4k_full_3800",
            "full"
        };
    for (String item : configs) {
      System.out.println("Testing " + item + ".json");
      String path = "com/mc/tool/caesar/vpm/videowall/unit/common/" + item + ".json";
      VideoWallGenerator videoWallGenerator =
          VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, VideoWallGenerator.VideoWallType.VP6);

      CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
      CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
      Vp6WallCurrCfg vp6WallCurrCfg = new Vp6WallCurrCfg();
      Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);

      List<VpConsoleData> vpcons =
          new ArrayList<>(
              videoWallGenerator
                  .getController()
                  .getDataModel()
                  .getConfigDataManager()
                  .getActiveVpconsolses());
      Map<Integer, Integer> id2Idx =
          IntStream.range(0, vpcons.size())
              .boxed()
              .collect(Collectors.toMap((idx) -> vpcons.get(idx).getId(), (idx) -> idx));
      for (int vpconId : vp6WallCurrCfg.getUsedVpCons()) {
        Vp6ConfigData configData = vp6WallCurrCfg.getVpconConfigData(vpconId);
        System.out.println(configData.toString());
        int index = id2Idx.get(vpconId);
        String resultPath =
            "com/mc/tool/caesar/vpm/videowall/unit/common/"
                + item
                + (index == 0 ? "" : "_" + index)
                + "_new.txt";
        checkResult(classLoader, configData, resultPath);
      }

      String resultPath = "com/mc/tool/caesar/vpm/videowall/unit/common/" + item + "_new.port.json";
      checkConnections(
          videoWallGenerator.getController(),
          classLoader,
          Vp6Processor.getConnections(vpMatrix, vp6WallCurrCfg),
          vpMatrix.findVpCon(vp6WallCurrCfg.getUsedVpCons().iterator().next()),
          resultPath);
    }
  }

  protected void checkResult(ClassLoader classLoader, Vp6ConfigData configData, String resultPath) {
    InputStream resultStream = classLoader.getResourceAsStream(resultPath);
    Reader resultReader = new InputStreamReader(resultStream);
    try {
      String result = CharStreams.toString(resultReader);
      Assert.assertEquals(result, configData.toString());
    } catch (IOException e) {
      Assert.fail();
    }
  }

  protected void checkConnections(
      CaesarDeviceController controller,
      ClassLoader classLoader,
      Map<ConsoleData, CpuData> connections,
      VpConsoleData vpConsoleData,
      String path) {
    InputStream resultStream = classLoader.getResourceAsStream(path);
    if (resultStream == null) {
      return;
    }
    Reader resultReader = new InputStreamReader(resultStream);
    try {
      String result = CharStreams.toString(resultReader);
      Map<String, Integer[]> connectTxIds =
          new Gson()
              .fromJson(
                  result,
                  new ParameterizedTypeImpl(Map.class, new Type[] {String.class, Integer[].class}));
      Integer[] txIds = connectTxIds.get("connectTxIds");
      Assert.assertEquals(txIds.length, connections.size());

      // 收集tx的索引
      int index = 0;
      Map<Integer, Integer> indexMapTxId = new HashMap<>();
      for (CpuData cpuData : controller.getDataModel().getConfigDataManager().getActiveCpus()) {
        indexMapTxId.put(index, cpuData.getExtenderData(0).getId());
        index++;
      }

      List<ConsoleData> portConsoleDatas = Arrays.asList(vpConsoleData.getInPortList());
      for (Map.Entry<ConsoleData, CpuData> entry : connections.entrySet()) {
        Integer portIndex = portConsoleDatas.indexOf(entry.getKey());
        int txIndex = txIds[portIndex];
        int txId = txIndex < 0 ? txIndex : indexMapTxId.get(txIndex);
        CpuData cpuData = entry.getValue();
        Assert.assertEquals(txId, cpuData == null ? -1 : cpuData.getExtenderData(0).getId());
      }
    } catch (IOException e) {
      Assert.fail();
    }
  }

  @Test
  public void testSinglePortSingle4k() {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_full.json";
    StatusConfig statusConfig = new StatusConfig();
    statusConfig.vp6PortSize = 1;
    VideoWallGenerator videoWallGenerator =
        VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, statusConfig);
    CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
    CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
    Vp6WallCurrCfg vp6WallCurrCfg = new Vp6WallCurrCfg();
    Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);
    Vp6ConfigData configData = vp6WallCurrCfg.getVpconConfigData(vp6WallCurrCfg.getUsedVpCons().iterator().next());
    System.out.println(configData.toString());

    String resultPath = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_full_new.txt";
    checkResult(classLoader, configData, resultPath);
  }

  /**
   * .
   */
  public void testChangeImpl(
      String configPath,
      String originResultPath,
      String changeResultPath,
      BiFunction<CaesarVideoWallData, VisualEditModel, Boolean> changeFunc) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    VideoWallGenerator videoWallGenerator =
        VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, configPath, VideoWallGenerator.VideoWallType.VP6);
    CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
    CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
    Vp6WallCurrCfg vp6WallCurrCfg = new Vp6WallCurrCfg();
    Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);
    Vp6ConfigData configData = vp6WallCurrCfg.getVpconConfigData(vp6WallCurrCfg.getUsedVpCons().iterator().next());
    System.out.println(configData.toString());
    checkResult(classLoader, configData, originResultPath);

    // 修改
    changeFunc.apply(videoWallGenerator.getVideoWallData(), videoWallGenerator.getModel());
    vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
    Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);
    configData = vp6WallCurrCfg.getVpconConfigData(vp6WallCurrCfg.getUsedVpCons().iterator().next());
    System.out.println(configData.toString());
    checkResult(classLoader, configData, changeResultPath);
  }

  /** 修改窗口大小测试. */
  @Test
  public void testResizeVideoConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin_new.txt";
    String resizeResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/resize_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        resizeResultPath,
        (data, model) -> {
          CaesarVideoData videoData = data.getVideos().get(1);
          videoData.getWidth().set(videoData.getWidth().get() - 100);
          return true;
        });
  }

  @Test
  public void testResize4kVideoConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin_new.txt";
    String resizeResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_resize_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        resizeResultPath,
        (data, model) -> {
          CaesarVideoData videoData = data.getVideos().get(1);
          videoData.getWidth().set(videoData.getWidth().get() - 100);
          return true;
        });
  }

  /** 上下层移动测试. */
  @Test
  public void testChangeLayerConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin_new.txt";
    String changeResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/changelayer_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        changeResultPath,
        (data, model) -> {
          // 上下层转换
          CaesarVideoData videoData = data.getVideos().remove(1);
          data.getVideos().add(0, videoData);
          return true;
        });
  }

  @Test
  public void testChangeLayer4kConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin_new.txt";
    String changeResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_changelayer_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        changeResultPath,
        (data, model) -> {
          // 上下层转换
          CaesarVideoData videoData = data.getVideos().remove(1);
          data.getVideos().add(0, videoData);
          return true;
        });
  }

  @Test
  public void testChangeLayerDhdmiConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin.json";
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_new.txt";
    String changeResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_changelayer_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        changeResultPath,
        (data, model) -> {
          // 上下层转换
          CaesarVideoData videoData = data.getVideos().remove(1);
          data.getVideos().add(0, videoData);
          return true;
        });
  }

  /** 添加窗口测试. */
  @Test
  public void testInsertVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin_new.txt";
    String addResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/add_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        addResultPath,
        (data, model) -> {
          // 插入一个视频窗口
          CaesarVideoData videoData = data.getVideos().get(0);
          CaesarVideoData newVideoData = new CaesarVideoData();
          videoData.copyTo(newVideoData);
          newVideoData.getWidth().set(newVideoData.getWidth().get() - 1);
          data.addVideo(1, newVideoData);
          return true;
        });
  }

  @Test
  public void testDhdmiInsert2kConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin.json";
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_new.txt";
    String changeResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_add_2k_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        changeResultPath,
        (data, model) -> {
          // 插入一个视频窗口
          CaesarVideoData videoData = data.getVideos().get(0);
          CaesarVideoData newVideoData = new CaesarVideoData();
          videoData.copyTo(newVideoData);
          newVideoData.getWidth().set(newVideoData.getWidth().get() - 1);
          data.addVideo(1, newVideoData);
          return true;
        });
  }

  @Test
  public void testDhdmiInsertDhdmiConfig() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin.json";
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_new.txt";
    String changeResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_add_dhmi_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        changeResultPath,
        (data, model) -> {
          // 插入一个视频窗口
          CaesarVideoData videoData = data.getVideos().get(2);
          CaesarVideoData newVideoData = new CaesarVideoData();
          videoData.copyTo(newVideoData);
          newVideoData.getWidth().set(newVideoData.getWidth().get() - 1);
          data.addVideo(2, newVideoData);
          return true;
        });
  }

  @Test
  public void testInsert4kTo2kVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin_new.txt";
    String add2ResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_add_4k_new.txt";

    testChangeImpl(
        path,
        originResultPath,
        add2ResultPath,
        (data, model) -> {
          // 插入一个4k窗口
          CaesarVideoData videoData = data.getVideos().get(0);
          CaesarVideoData newVideoData = new CaesarVideoData();
          videoData.copyTo(newVideoData);

          newVideoData.getWidth().set(newVideoData.getWidth().get() - 1);
          List<VisualEditTerminal> terminals =
              CaesarDataUtility.getFirstMatrix(model).getAllTxChildTerminal();
          CaesarCpuTerminal terminal = (CaesarCpuTerminal) terminals.get(terminals.size() - 1);
          ResolutionData res4k = new ResolutionData();
          res4k.setWidth(3840);
          res4k.setHeight(2160);
          terminal.getExtenderData().setResolution(0, res4k);
          newVideoData.getSource().set(terminal);
          data.addVideo(1, newVideoData);
          return true;
        });
  }

  @Test
  public void testInsert4kTo4kVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin_new.txt";
    String addResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_add_4k_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        addResultPath,
        (data, model) -> {
          // 插入一个4k窗口
          CaesarVideoData videoData = data.getVideos().get(0);
          CaesarVideoData newVideoData = new CaesarVideoData();
          videoData.copyTo(newVideoData);

          newVideoData.getWidth().set(newVideoData.getWidth().get() - 1);
          List<VisualEditTerminal> terminals =
              CaesarDataUtility.getFirstMatrix(model).getAllTxChildTerminal();
          CaesarCpuTerminal terminal = (CaesarCpuTerminal) terminals.get(terminals.size() - 1);
          ResolutionData res4k = new ResolutionData();
          res4k.setWidth(3840);
          res4k.setHeight(2160);
          terminal.getExtenderData().setResolution(0, res4k);
          newVideoData.getSource().set(terminal);
          data.addVideo(1, newVideoData);
          return true;
        });
  }

  @Test
  public void testInsert2kTo4kVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin_new.txt";
    String addResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_add_2k_new.txt";
    testChangeImpl(
        path,
        originResultPath,
        addResultPath,
        (data, model) -> {
          // 插入一个2k窗口
          CaesarVideoData videoData = data.getVideos().get(0);
          CaesarVideoData newVideoData = new CaesarVideoData();
          videoData.copyTo(newVideoData);

          newVideoData.getWidth().set(newVideoData.getWidth().get() - 1);
          List<VisualEditTerminal> terminals =
              CaesarDataUtility.getFirstMatrix(model).getAllTxChildTerminal();
          CaesarCpuTerminal terminal = (CaesarCpuTerminal) terminals.get(terminals.size() - 1);
          ResolutionData res4k = new ResolutionData();
          res4k.setWidth(1920);
          res4k.setHeight(1080);
          terminal.getExtenderData().setResolution(0, res4k);
          newVideoData.getSource().set(terminal);
          data.addVideo(1, newVideoData);
          return true;
        });
  }

  /** 删除窗口测试. */
  @Test
  public void testDeleteDhdmiLeftVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin.json";
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_new.txt";
    String deleteResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_delete_left_new.txt";

    testChangeImpl(
        path,
        originResultPath,
        deleteResultPath,
        (data, model) -> {
          // 删除一个视频窗口
          data.getVideos().remove(1);
          return true;
        });
  }

  /** 删除窗口测试. */
  @Test
  public void testDeleteDhdmiRightVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin.json";
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_new.txt";
    String deleteResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_dual_origin_delete_right_new.txt";

    testChangeImpl(
        path,
        originResultPath,
        deleteResultPath,
        (data, model) -> {
          // 删除一个视频窗口
          data.getVideos().remove(2);
          return true;
        });
  }

  @Test
  public void testAddDhdmiLeftVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_right_origin.json";
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_right_origin_new.txt";
    String deleteResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/dhdmi_right_origin_add_left_new.txt";

    testChangeImpl(
        path,
        originResultPath,
        deleteResultPath,
        (data, model) -> {
          // 添加一个视频窗口
          CaesarVideoData video0 = data.getVideos().get(0);
          CaesarVideoData video1 = new CaesarVideoData();
          video0.copyTo(video1);
          video1.getSourceIndex().set(0);
          video1.getXpos().set(960);
          data.getVideos().add(video1);
          return true;
        });
  }

  /** 删除窗口测试. */
  @Test
  public void testDeleteVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/origin_new.txt";
    String deleteResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/delete_new.txt";

    testChangeImpl(
        path,
        originResultPath,
        deleteResultPath,
        (data, model) -> {
          // 删除一个视频窗口
          data.getVideos().remove(1);
          return true;
        });
  }

  @Test
  public void testDelete4kVideo() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_origin_new.txt";
    String deleteResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/4k_delete_new.txt";

    testChangeImpl(
        path,
        originResultPath,
        deleteResultPath,
        (data, model) -> {
          // 删除一个视频窗口
          data.getVideos().remove(0);
          return true;
        });
  }

  @Test
  public void test2kFullReplace4k() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_origin.json";
    String originResultPath = "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_origin.txt";
    String replaceUpResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_replace_up_4k.txt";
    String replaceDownResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_replace_down_4k.txt";

    testChangeImpl(
        path,
        originResultPath,
        replaceUpResultPath,
        (data, model) -> {
          // 删除4个2k视频窗口
          data.getVideos().removeIf((video) -> video.getHeight().get() % 2 == 0);
          // 添加两个4k
          for (int i = 0; i < 2; i++) {
            CaesarVideoData newVideoData = new CaesarVideoData();
            data.getVideos().get(i).copyTo(newVideoData);
            newVideoData.getHeight().set(newVideoData.getHeight().get() + 1);
            List<VisualEditTerminal> terminals =
                CaesarDataUtility.getFirstMatrix(model).getAllTxChildTerminal();
            CaesarCpuTerminal terminal =
                (CaesarCpuTerminal) terminals.get(terminals.size() - 1 - i);
            ResolutionData res4k = new ResolutionData();
            res4k.setWidth(3840);
            res4k.setHeight(2160);
            terminal.getExtenderData().setResolution(0, res4k);
            newVideoData.getSource().set(terminal);

            data.getVideos().add(newVideoData);
          }
          return true;
        });

    testChangeImpl(
        path,
        originResultPath,
        replaceDownResultPath,
        (data, model) -> {
          // 删除4个2k视频窗口
          data.getVideos().removeIf((video) -> video.getHeight().get() % 2 == 1);
          // 添加两个4k
          for (int i = 0; i < 2; i++) {
            CaesarVideoData newVideoData = new CaesarVideoData();
            data.getVideos().get(i).copyTo(newVideoData);
            newVideoData.getHeight().set(newVideoData.getHeight().get() + 1);
            List<VisualEditTerminal> terminals =
                CaesarDataUtility.getFirstMatrix(model).getAllTxChildTerminal();
            CaesarCpuTerminal terminal =
                (CaesarCpuTerminal) terminals.get(terminals.size() - 1 - i);
            ResolutionData res4k = new ResolutionData();
            res4k.setWidth(3840);
            res4k.setHeight(2160);
            terminal.getExtenderData().setResolution(0, res4k);
            newVideoData.getSource().set(terminal);

            data.getVideos().add(newVideoData);
          }
          return true;
        });
  }

  @Test
  public void test2kFullReplace4kWithOfflinePort() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_origin.json";

    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    VideoWallGenerator videoWallGenerator =
        VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, path, VideoWallGenerator.VideoWallType.VP6);
    // 令 6、7号端口离线
    Collection<VpConsoleData> vps =
        videoWallGenerator
            .getController()
            .getDataModel()
            .getConfigDataManager()
            .getActiveVpconsolses();
    VpConsoleData vpConsoleData = vps.iterator().next();
    vpConsoleData.getInPort(6).getExtenderData(0).setPort(0);
    vpConsoleData.getInPort(7).getExtenderData(0).setPort(0);
    // 计算
    CaesarVpMatrix vpMatrix = new CaesarVpMatrix(videoWallGenerator.getController());
    CaesarVpVideoWall vpVideoWall = new CaesarVpVideoWall(videoWallGenerator.getVideoWallData());
    Vp6WallCurrCfg vp6WallCurrCfg = new Vp6WallCurrCfg();
    Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);
    Vp6ConfigData configData = vp6WallCurrCfg.getVpconConfigData(vp6WallCurrCfg.getUsedVpCons().iterator().next());
    System.out.println(configData.toString());
    String originResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_offline_origin.txt";
    checkResult(classLoader, configData, originResultPath);

    // 删除1、3、6、7
    CaesarVideoWallData videoWallData = videoWallGenerator.getVideoWallData();
    videoWallData
        .getVideos()
        .removeAll(
            videoWallData.getVideos().get(1),
            videoWallData.getVideos().get(3),
            videoWallData.getVideos().get(6),
            videoWallData.getVideos().get(7));
    // 添加2个4k窗口
    for (int i = 0; i < 2; i++) {
      CaesarVideoData newVideoData = new CaesarVideoData();
      videoWallData.getVideos().get(i).copyTo(newVideoData);
      newVideoData.getHeight().set(newVideoData.getHeight().get() + 1);
      List<VisualEditTerminal> terminals =
          CaesarDataUtility.getFirstMatrix(videoWallGenerator.getModel()).getAllTxChildTerminal();
      CaesarCpuTerminal terminal = (CaesarCpuTerminal) terminals.get(terminals.size() - 1 - i);
      ResolutionData res4k = new ResolutionData();
      res4k.setWidth(3840);
      res4k.setHeight(2160);
      terminal.getExtenderData().setResolution(0, res4k);
      newVideoData.getSource().set(terminal);

      videoWallData.getVideos().add(newVideoData);
    }

    // 再次计算
    Vp6Processor.processVideoWall(vpMatrix, vpVideoWall, vp6WallCurrCfg);
    configData = vp6WallCurrCfg.getVpconConfigData(vp6WallCurrCfg.getUsedVpCons().iterator().next());
    System.out.println(configData.toString());
    String replaceResultPath =
        "com/mc/tool/caesar/vpm/videowall/unit/modify/2k_full_offline_replace_origin.txt";
    checkResult(classLoader, configData, replaceResultPath);
  }
}
